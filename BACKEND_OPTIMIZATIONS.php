<?php
/**
 * Backend Optimizations for Farmasi DataTables
 * 
 * This file contains optimized versions of controller methods
 * Apply these changes to application/controllers/Farmasi.php
 */

// 1. OPTIMIZED tabelResepBaru METHOD
public function tabelResepBaru()
{
    $session = $this->session->get_userdata();
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        try {
            $post = $this->input->post();
            $norm = $post['norm'];
            $draw = intval($post['draw']);
            $start = intval($_POST['start']);
            $length = intval($_POST['length']);
            $search = $_POST['search']['value'] ?? '';
            
            $view_only = $this->input->post('view_only') ?: null;
            $id_layanan = $this->input->post('id_layanan') ?: null;

            // Add caching for better performance
            $cache_key = "resep_" . md5($norm . $draw . $start . $length . $search);
            if ($cached = $this->cache->get($cache_key)) {
                echo json_encode($cached);
                return;
            }

            // Get total count first (more efficient)
            $totalRecords = $this->ResepModel->hitungSemua($norm, $id_layanan, $view_only);
            
            // Get filtered count
            $filteredRecords = $this->ResepModel->hitungTersaring($norm, $id_layanan, $view_only, $search);
            
            // Get actual data with limit
            $tabel = $this->ResepModel->ambilTabelOptimized($norm, $start, $length, $search, $id_layanan, $view_only);

            if (!is_array($tabel)) {
                throw new Exception('Gagal mengambil data tabel');
            }

            $data = [];
            $no = $start + 1;

            foreach ($tabel as $row) {
                // Optimize checkbox generation
                $isChecked = $this->ResepModel->isResepSelected($row->NOKUN, $id_layanan);
                $checkbox = '<input type="checkbox" class="pilihResep styled-checkbox" value="' . $row->NOKUN . '"' . 
                           ($isChecked ? ' checked' : '') . '>';

                $data[] = [
                    $no++,
                    $row->ASAL_UNIT ?? '-',
                    $row->TUJUAN ?? '-',
                    $row->sep_resep ?? '-',
                    $row->OLEH ?? '-',
                    $row->TANGGAL_RESEP ?? '-',
                    $row->STATUS_RESEP ?? '-',
                    '<button class="btn btn-info btn-sm tbl-resep-farmasi" data-toggle="modal" data-target="#modal-resep-farmasi" data-id="' . $row->NOKUN . '">
                        <i class="fa fa-eye"></i> Lihat
                    </button>',
                    $checkbox
                ];
            }

            $output = [
                'draw' => $draw,
                'recordsTotal' => $totalRecords,
                'recordsFiltered' => $filteredRecords,
                'data' => $data
            ];

            // Cache result for 5 minutes
            $this->cache->save($cache_key, $output, 300);
            
            echo json_encode($output);

        } catch (Exception $e) {
            echo json_encode([
                'draw' => intval($this->input->post('draw')),
                'recordsTotal' => 0,
                'recordsFiltered' => 0,
                'data' => [],
                'error' => $e->getMessage()
            ]);
        }
    }
}

// 2. OPTIMIZED tablePK METHOD
public function tablePK()
{
    $norm = $this->input->post('norm');
    $limit = intval($this->input->post('length')) ?: 10;
    $start = intval($this->input->post('start')) ?: 0;
    $searchValue = $this->input->post('search')['value'] ?? '';
    
    $id_layanan = $this->input->post('id_layanan') ?: null;
    $view_only = $this->input->post('view_only') ?: null;

    // Add caching
    $cache_key = "pk_" . md5($norm . $limit . $start . $searchValue);
    if ($cached = $this->cache->get($cache_key)) {
        echo json_encode($cached);
        return;
    }

    // Optimized count queries
    $totalData = $this->PKModel->count_kunjungan_pk_optimized($norm, $id_layanan, $view_only);
    $filteredData = $this->PKModel->count_filtered_kunjungan_pk_optimized($norm, $searchValue, $id_layanan, $view_only);
    
    // Get data with optimized query
    $results = $this->PKModel->get_kunjungan_pk_optimized($norm, $limit, $start, $searchValue, $id_layanan, $view_only);

    $data = [];
    foreach ($results as $row) {
        $isChecked = $this->PKModel->isPKSelected($row['nokun'], $id_layanan);
        $checkbox = '<input type="checkbox" class="pilihPK styled-checkbox" value="' . $row['nokun'] . '"' . 
                   ($isChecked ? ' checked' : '') . '>';

        $data[] = [
            'pilih' => $checkbox,
            'masuk' => $row['TGLMASUK'],
            'tindakan' => $row['TINDAKAN'],
            'nokun' => $row['nokun'],
            'aksi' => '<a class="btn btn-info btn-sm viewPk" data-id="' . $row['nokun'] . '">
                        <i class="fa fa-eye"></i> Lihat
                      </a>'
        ];
    }

    $output = [
        'draw' => intval($this->input->post('draw')),
        'recordsTotal' => $totalData,
        'recordsFiltered' => $filteredData,
        'data' => $data
    ];

    // Cache for 5 minutes
    $this->cache->save($cache_key, $output, 300);
    
    echo json_encode($output);
}

// 3. OPTIMIZED get_data_sito METHOD
public function get_data_sito()
{
    $draw = intval($this->input->post("draw"));
    $start = intval($this->input->post("start"));
    $length = intval($this->input->post("length"));
    $search = $this->input->post('search')['value'] ?? '';
    $view_only = $this->input->post('view_only') ?: null;
    $mr = $this->input->post('mr', true);

    // Add caching
    $cache_key = "sito_" . md5($mr . $start . $length . $search);
    if ($cached = $this->cache->get($cache_key)) {
        echo json_encode($cached);
        return;
    }

    // Optimized queries
    $totalCount = $this->PAModel->total_count_Sito_optimized($mr, $view_only);
    $filteredCount = $this->PAModel->filter_count_Sito_optimized($mr, $view_only, $search);
    $listSito = $this->PAModel->datatablesSito_optimized($mr, $start, $length, $search, $view_only);

    $data = [];
    $no = $start + 1;

    foreach ($listSito as $row) {
        $isChecked = $this->PAModel->isSitoSelected($row->ID, $view_only);
        $checkbox = '<input type="checkbox" class="cekSito styled-checkbox" value="' . $row->ID . '"' . 
                   ($isChecked ? ' checked' : '') . '>';

        $data[] = [
            $no++,
            $row->NOMOR_LAB,
            $row->TANGGAL_ORDER,
            '<button class="btn btn-info btn-sm tbl-sitologi-farmasi" data-toggle="modal" data-target="#modal-sitologi-farmasi" data-lab="' . $row->NOMOR_LAB . '">
                <i class="fa fa-eye"></i> Lihat
            </button>',
            $checkbox
        ];
    }

    $output = [
        'draw' => $draw,
        'recordsTotal' => $totalCount,
        'recordsFiltered' => $filteredCount,
        'data' => $data
    ];

    // Cache for 10 minutes (PA data changes less frequently)
    $this->cache->save($cache_key, $output, 600);
    
    echo json_encode($output);
}

// 4. OPTIMIZED tableOldData METHOD
public function tableOldData()
{
    $post = $this->input->post();
    $no_mr = $post['nomorMr'] ?? null;
    $start = intval($_POST['start']) ?: 0;
    $length = intval($_POST['length']) ?: 10;
    $search = $_POST['search']['value'] ?? '';
    
    $view_only = $post['viewOnly'] ?? null;

    // Add caching
    $cache_key = "old_data_" . md5($no_mr . $start . $length . $search);
    if ($cached = $this->cache->get($cache_key)) {
        echo json_encode($cached);
        return;
    }

    // Optimized queries
    $total = $this->FarmasiModel->hitungSemua_optimized(null, null, $no_mr, null, $view_only);
    $filtered = $this->FarmasiModel->hitungTersaring_optimized(null, null, $no_mr, null, $view_only, $search);
    $list = $this->FarmasiModel->ambilTabel_optimized(null, null, $no_mr, null, $view_only, $start, $length, $search);

    $data = [];
    $no = $start + 1;

    foreach ($list as $row) {
        $isChecked = $this->FarmasiModel->isOldDataSelected($row->id, $view_only);
        $checkbox = '<input type="checkbox" class="inpOldTable styled-checkbox" value="' . $row->id . '"' . 
                   ($isChecked ? ' checked' : '') . '>';

        $data[] = [
            $no++,
            date('d/m/Y', strtotime($row->tgllayanan)),
            $row->jenis_data,
            '<button class="btn btn-info btn-sm">
                <i class="fa fa-eye"></i> Lihat
            </button>',
            $checkbox
        ];
    }

    $output = [
        'draw' => intval($_POST['draw']),
        'recordsTotal' => $total,
        'recordsFiltered' => $filtered,
        'data' => $data
    ];

    // Cache for 15 minutes
    $this->cache->save($cache_key, $output, 900);
    
    echo json_encode($output);
}

/**
 * ADDITIONAL HELPER METHODS TO ADD TO MODELS
 */

// Add to ResepModel.php
public function ambilTabelOptimized($norm, $start, $length, $search = '', $id_layanan = '', $view_only = null)
{
    $this->db->select('ore.NOMOR as NOKUN, r.DESKRIPSI as ASAL_UNIT, ras.DESKRIPSI as TUJUAN, 
                       IFNULL(pj.NOMOR, "-") as sep_resep, 
                       IF(ore.OLEH > 0, IFNULL(master.getNamaLengkapPegawai(ap.NIP), ore.PEMBERI_RESEP), ore.PEMBERI_RESEP) as OLEH,
                       DATE_FORMAT(ore.TANGGAL, "%d/%m/%Y, %H.%i.%s") as TANGGAL_RESEP,
                       IF(ore.STATUS = 1, "Order Belum Diterima", "Processed") as STATUS_RESEP');
    
    $this->db->from('layanan.order_resep ore');
    $this->db->join('pendaftaran.kunjungan pk', 'pk.NOMOR = ore.KUNJUNGAN', 'left');
    $this->db->join('pendaftaran.pendaftaran pp', 'pp.NOMOR = pk.NOPEN', 'left');
    $this->db->join('pendaftaran.penjamin pj', 'pj.NOPEN = pp.NOMOR AND pj.JENIS=2', 'left');
    $this->db->join('master.ruangan r', 'r.ID = pk.RUANGAN', 'left');
    $this->db->join('master.ruangan ras', 'ras.ID = ore.TUJUAN', 'left');
    $this->db->join('aplikasi.pengguna ap', 'ore.OLEH = ap.ID', 'left');
    
    $this->db->where('pp.NORM', $norm);
    
    if (!empty($search)) {
        $this->db->group_start();
        $this->db->like('r.DESKRIPSI', $search);
        $this->db->or_like('ras.DESKRIPSI', $search);
        $this->db->or_like('ore.PEMBERI_RESEP', $search);
        $this->db->group_end();
    }
    
    $this->db->order_by('ore.TANGGAL', 'DESC');
    $this->db->limit($length, $start);
    
    return $this->db->get()->result();
}

// Add to PKModel.php  
public function get_kunjungan_pk_optimized($norm, $limit, $start, $searchValue = null, $id_layanan = null, $view_only = 0)
{
    $this->db->select("pk.NOMOR as nokun, 
                       DATE_FORMAT(pk.MASUK, '%d/%m/%Y') as TGLMASUK,
                       GROUP_CONCAT(t.NAMA SEPARATOR ', ') as TINDAKAN");
    
    $this->db->from('pendaftaran.pendaftaran pp');
    $this->db->join('pendaftaran.kunjungan pk', 'pp.NOMOR = pk.NOPEN');
    $this->db->join('layanan.tindakan_medis tm', 'tm.KUNJUNGAN = pk.NOMOR', 'left');
    $this->db->join('master.tindakan t', 'tm.TINDAKAN = t.ID', 'left');
    
    $this->db->where('pp.NORM', $norm);
    $this->db->where_in('pk.RUANGAN', ['105070101', '105070102']);
    
    if (!empty($searchValue)) {
        $this->db->group_start();
        $this->db->like('t.NAMA', $searchValue);
        $this->db->or_like('pk.MASUK', $searchValue);
        $this->db->group_end();
    }
    
    $this->db->group_by('pk.NOMOR');
    $this->db->order_by('pk.MASUK', 'DESC');
    $this->db->limit($limit, $start);
    
    return $this->db->get()->result_array();
}
?>
