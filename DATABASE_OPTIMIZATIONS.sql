-- DATABASE OPTIMIZATIONS FOR FARMASI DATATABLES
-- Execute these queries to improve database performance

-- =====================================================
-- 1. ADD MISSING INDEXES FOR BETTER QUERY PERFORMANCE
-- =====================================================

-- Indexes for pendaftaran.pendaftaran table
ALTER TABLE pendaftaran.pendaftaran 
ADD INDEX idx_norm (NORM),
ADD INDEX idx_nomor (NOMOR);

-- Indexes for pendaftaran.kunjungan table  
ALTER TABLE pendaftaran.kunjungan 
ADD INDEX idx_nopen (NOPEN),
ADD INDEX idx_ruangan (RUANGAN),
ADD INDEX idx_masuk (MASUK),
ADD INDEX idx_nopen_ruangan (NOPEN, RUANGAN),
ADD INDEX idx_ref (REF);

-- Indexes for layanan.order_resep table
ALTER TABLE layanan.order_resep 
ADD INDEX idx_kunjungan (KUNJUNGAN),
ADD INDEX idx_nomor (NOMOR),
ADD INDEX idx_status (STATUS),
ADD INDEX idx_tanggal (TANGGAL),
ADD INDEX idx_tujuan (TUJUAN),
ADD INDEX idx_oleh (OLEH);

-- Indexes for bpjs.uploads_bpjs table
ALTER TABLE bpjs.uploads_bpjs 
ADD INDEX idx_norm (norm),
ADD INDEX idx_tgllayanan (tgllayanan),
ADD INDEX idx_norm_tgl (norm, tgllayanan),
ADD INDEX idx_jenis_data (jenis_data);

-- Indexes for bpjs.verifikasi_awal table
ALTER TABLE bpjs.verifikasi_awal 
ADD INDEX idx_norm (norm),
ADD INDEX idx_created_at (created_at);

-- Indexes for bpjs.verif_uploads_bpjs table
ALTER TABLE bpjs.verif_uploads_bpjs 
ADD INDEX idx_id_verifikasi (id_verifikasi),
ADD INDEX idx_id_uploads_bpjs (id_uploads_bpjs);

-- Indexes for layanan.tindakan_medis table
ALTER TABLE layanan.tindakan_medis 
ADD INDEX idx_kunjungan (KUNJUNGAN),
ADD INDEX idx_tindakan (TINDAKAN),
ADD INDEX idx_status (STATUS),
ADD INDEX idx_tanggal (TANGGAL);

-- Indexes for master.tindakan table
ALTER TABLE master.tindakan 
ADD INDEX idx_nama (NAMA),
ADD INDEX idx_id (ID);

-- =====================================================
-- 2. OPTIMIZE EXISTING QUERIES WITH BETTER STRUCTURE
-- =====================================================

-- Create optimized view for resep data
CREATE OR REPLACE VIEW v_resep_optimized AS
SELECT 
    ore.NOMOR as NOKUN,
    ore.TANGGAL,
    ore.STATUS,
    ore.PEMBERI_RESEP,
    ore.OLEH,
    pp.NORM,
    r.DESKRIPSI as ASAL_UNIT,
    ras.DESKRIPSI as TUJUAN,
    IFNULL(pj.NOMOR, '-') as sep_resep,
    IF(ore.OLEH > 0, 
       IFNULL(master.getNamaLengkapPegawai(ap.NIP), ore.PEMBERI_RESEP), 
       ore.PEMBERI_RESEP) as OLEH_NAMA,
    DATE_FORMAT(ore.TANGGAL, '%d/%m/%Y, %H.%i.%s') as TANGGAL_FORMATTED,
    CASE 
        WHEN ore.STATUS = 1 THEN 'Order Belum Diterima'
        WHEN ore.STATUS = 2 AND pk.STATUS = 1 THEN 'Diterima/diproses'
        WHEN ore.STATUS = 2 AND pk.STATUS = 2 THEN 'Resep Difinalkan'
        WHEN ore.STATUS = 0 THEN 'Order Dibatalkan'
        ELSE 'Resep Dibatalkan'
    END as STATUS_RESEP
FROM layanan.order_resep ore
LEFT JOIN pendaftaran.kunjungan pkk ON pkk.NOMOR = ore.KUNJUNGAN
LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pkk.NOPEN
LEFT JOIN pendaftaran.penjamin pj ON pj.NOPEN = pp.NOMOR AND pj.JENIS = 2
LEFT JOIN pendaftaran.kunjungan pk ON pk.REF = ore.NOMOR
LEFT JOIN master.ruangan r ON r.ID = pkk.RUANGAN
LEFT JOIN master.ruangan ras ON ras.ID = ore.TUJUAN
LEFT JOIN aplikasi.pengguna ap ON ore.OLEH = ap.ID;

-- Create optimized view for PK data
CREATE OR REPLACE VIEW v_pk_optimized AS
SELECT 
    pk.NOMOR as nokun,
    pk.NOPEN,
    pk.MASUK,
    pp.NORM,
    DATE_FORMAT(pk.MASUK, '%d/%m/%Y') as TGLMASUK,
    masR.DESKRIPSI as RUANGAN,
    GROUP_CONCAT(t.NAMA SEPARATOR ', ') as TINDAKAN
FROM pendaftaran.kunjungan pk
LEFT JOIN pendaftaran.pendaftaran pp ON pp.NOMOR = pk.NOPEN
LEFT JOIN master.ruangan masR ON pk.RUANGAN = masR.ID
LEFT JOIN layanan.tindakan_medis tm ON tm.KUNJUNGAN = pk.NOMOR AND tm.STATUS != 0
LEFT JOIN master.tindakan t ON tm.TINDAKAN = t.ID
WHERE pk.RUANGAN IN ('105070101', '105070102')
GROUP BY pk.NOMOR, pk.NOPEN, pk.MASUK, pp.NORM, masR.DESKRIPSI;

-- =====================================================
-- 3. CREATE MATERIALIZED VIEWS FOR HEAVY QUERIES
-- =====================================================

-- Note: MySQL doesn't support materialized views natively
-- But we can create tables that act as materialized views

-- Create summary table for uploads_bpjs statistics
CREATE TABLE IF NOT EXISTS bpjs.uploads_summary (
    norm VARCHAR(20),
    total_uploads INT,
    latest_upload DATETIME,
    jenis_data_count JSON,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (norm),
    INDEX idx_latest_upload (latest_upload)
);

-- Populate the summary table
INSERT INTO bpjs.uploads_summary (norm, total_uploads, latest_upload, jenis_data_count)
SELECT 
    norm,
    COUNT(*) as total_uploads,
    MAX(tgllayanan) as latest_upload,
    JSON_OBJECT(
        'total', COUNT(*),
        'by_type', JSON_OBJECTAGG(jenis_data, cnt)
    ) as jenis_data_count
FROM (
    SELECT 
        norm, 
        tgllayanan, 
        jenis_data,
        COUNT(*) as cnt
    FROM bpjs.uploads_bpjs 
    GROUP BY norm, jenis_data
) grouped
GROUP BY norm
ON DUPLICATE KEY UPDATE
    total_uploads = VALUES(total_uploads),
    latest_upload = VALUES(latest_upload),
    jenis_data_count = VALUES(jenis_data_count);

-- =====================================================
-- 4. STORED PROCEDURES FOR COMPLEX QUERIES
-- =====================================================

DELIMITER //

-- Stored procedure for getting resep data with pagination
CREATE PROCEDURE GetResepData(
    IN p_norm VARCHAR(20),
    IN p_start INT,
    IN p_length INT,
    IN p_search VARCHAR(255),
    IN p_id_layanan INT,
    IN p_view_only TINYINT
)
BEGIN
    DECLARE v_sql TEXT;
    
    SET v_sql = 'SELECT * FROM v_resep_optimized WHERE NORM = ?';
    
    IF p_search IS NOT NULL AND p_search != '' THEN
        SET v_sql = CONCAT(v_sql, ' AND (ASAL_UNIT LIKE ? OR TUJUAN LIKE ? OR PEMBERI_RESEP LIKE ?)');
    END IF;
    
    SET v_sql = CONCAT(v_sql, ' ORDER BY TANGGAL DESC LIMIT ?, ?');
    
    SET @sql = v_sql;
    
    IF p_search IS NOT NULL AND p_search != '' THEN
        SET @search_param = CONCAT('%', p_search, '%');
        EXECUTE @sql USING p_norm, @search_param, @search_param, @search_param, p_start, p_length;
    ELSE
        EXECUTE @sql USING p_norm, p_start, p_length;
    END IF;
    
    DEALLOCATE PREPARE @sql;
END //

-- Stored procedure for getting PK data with pagination
CREATE PROCEDURE GetPKData(
    IN p_norm VARCHAR(20),
    IN p_start INT,
    IN p_length INT,
    IN p_search VARCHAR(255)
)
BEGIN
    DECLARE v_sql TEXT;
    
    SET v_sql = 'SELECT * FROM v_pk_optimized WHERE NORM = ?';
    
    IF p_search IS NOT NULL AND p_search != '' THEN
        SET v_sql = CONCAT(v_sql, ' AND (TINDAKAN LIKE ? OR TGLMASUK LIKE ?)');
    END IF;
    
    SET v_sql = CONCAT(v_sql, ' ORDER BY MASUK DESC LIMIT ?, ?');
    
    SET @sql = v_sql;
    
    IF p_search IS NOT NULL AND p_search != '' THEN
        SET @search_param = CONCAT('%', p_search, '%');
        EXECUTE @sql USING p_norm, @search_param, @search_param, p_start, p_length;
    ELSE
        EXECUTE @sql USING p_norm, p_start, p_length;
    END IF;
    
    DEALLOCATE PREPARE @sql;
END //

DELIMITER ;

-- =====================================================
-- 5. QUERY OPTIMIZATION SETTINGS
-- =====================================================

-- Optimize MySQL settings for better performance
SET GLOBAL query_cache_size = 268435456; -- 256MB
SET GLOBAL query_cache_type = ON;
SET GLOBAL tmp_table_size = 134217728; -- 128MB
SET GLOBAL max_heap_table_size = 134217728; -- 128MB
SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB (adjust based on available RAM)

-- =====================================================
-- 6. MAINTENANCE QUERIES
-- =====================================================

-- Analyze tables for better query planning
ANALYZE TABLE pendaftaran.pendaftaran;
ANALYZE TABLE pendaftaran.kunjungan;
ANALYZE TABLE layanan.order_resep;
ANALYZE TABLE bpjs.uploads_bpjs;
ANALYZE TABLE layanan.tindakan_medis;

-- Optimize tables
OPTIMIZE TABLE pendaftaran.pendaftaran;
OPTIMIZE TABLE pendaftaran.kunjungan;
OPTIMIZE TABLE layanan.order_resep;
OPTIMIZE TABLE bpjs.uploads_bpjs;

-- =====================================================
-- 7. MONITORING QUERIES
-- =====================================================

-- Query to check slow queries
SELECT 
    query_time,
    lock_time,
    rows_sent,
    rows_examined,
    sql_text
FROM mysql.slow_log 
WHERE sql_text LIKE '%farmasi%' 
ORDER BY query_time DESC 
LIMIT 10;

-- Query to check index usage
SELECT 
    TABLE_SCHEMA,
    TABLE_NAME,
    INDEX_NAME,
    CARDINALITY,
    SUB_PART,
    PACKED,
    NULLABLE,
    INDEX_TYPE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA IN ('pendaftaran', 'layanan', 'bpjs')
ORDER BY TABLE_NAME, INDEX_NAME;
