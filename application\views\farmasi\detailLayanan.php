<style>
    /* Styled Checkbox */
    .styled-checkbox {
        position: relative;
        appearance: none;
        width: 20px;
        height: 20px;
        background-color: #f1f1f1;
        border: 2px solid #ccc;
        border-radius: 4px;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    /* On hover, slightly enlarge the checkbox */
    .styled-checkbox:hover {
        transform: scale(1.1);
    }

    /* Add checkmark animation */
    .styled-checkbox:checked {
        background-color: #4CAF50;
        border-color: #4CAF50;
        animation: checkboxAnimation 0.3s ease forwards;
    }

    /* Checkmark and its animation */
    .styled-checkbox:checked::after {
        content: '';
        position: absolute;
        left: 6px;
        top: 2px;
        width: 6px;
        height: 12px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
    }

    /* Animation Keyframes */
    @keyframes checkboxAnimation {
        0% {
            transform: scale(0);
        }

        100% {
            transform: scale(1);
        }
    }
</style>
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-dark"><?= $header ? $header : null ?></h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('farmasi/data') ?>">Data Farmasi</a>
                    </li>
                    <li class="breadcrumb-item active">
                        <a href="<?= base_url('Farmasi/formData') ?>">Input Data</a>
                    </li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Content -->
<div class="content">
    <div class="container-fluid">
        <div class="row">
            <!-- Patient Information Form (Nomor Rekam Medis, BPJS, Nama Pasien) -->
            <div class="col-sm-12 my-2">
                <div class="card">
                    <div class="card-body">
                        <div class="form-group row">
                            <!-- Nomor Rekam Medis -->
                            <div class="col-md-3">
                                <label for="no-rm-farmasi">Nomor Rekam Medis</label>
                                <input type="text" class="form-control" id="no-rm-farmasi" name="norm"
                                    value="<?= isset($data_pasien->norm) ? $data_pasien->norm : '' ?>"
                                    placeholder="Nomor Rekam Medis" maxlength="6" readonly>
                            </div>
                            <!-- Nomor Kartu BPJS -->
                            <div class="col-md-4">
                                <label for="no-bpjs-farmasi">Nomor Kartu BPJS</label>
                                <input type="text" class="form-control" id="no-bpjs-farmasi" name="nobpjs"
                                    value="<?= isset($data_pasien->no_bpjs) ? $data_pasien->no_bpjs : '' ?>"
                                    placeholder="Nomor BPJS atau Jaminan" readonly>
                            </div>
                            <!-- Nama Pasien -->
                            <div class="col-md-5">
                                <label for="nama-pasien-farmasi">Nama Pasien</label>
                                <input type="text" class="form-control" id="nama-pasien-farmasi" name="nama"
                                    value="<?= isset($data_pasien->nama_pasien) ? $data_pasien->nama_pasien : '' ?>"
                                    placeholder="Nama Pasien" readonly>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-sm-12 my-2">
                <div class="card">
                    <div class="card-body">
                        <h3 class="fw-bold">Dokumen Verifikasi Awal Sebelumnya</h3>
                        <div class="row">
                            <div class="table-responsive mt-3">
                                <table class="table table-bordered tableDoc" id="tableDoc">
                                    <thead>
                                        <tr>
                                            <th>Jenis Dokumen</th>
                                            <th>Nama File</th>
                                            <th>Tanggal Upload</th>
                                            <th>Aksi</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 my-2">
                <div class="card">
                    <div class="card-body">
                        <h3 class="fw-bold">Data Verifikasi Awal</h3>
                        <!-- Tabs for HASIL PA and HASIL RAD inside the same card -->
                        <ul class="nav nav-tabs" id="resultTabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="data-lama-tab" data-toggle="tab" href="#data-lama" role="tab" aria-controls="data-lama" aria-selected="true">DATA LAMA</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="hasil-pa-tab" data-toggle="tab" href="#hasil-pa" role="tab" aria-controls="hasil-pa" aria-selected="false">HASIL PA</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="hasil-rad-tab" data-toggle="tab" href="#hasil-rad" role="tab" aria-controls="hasil-rad" aria-selected="false">HASIL RADIOLOGI</a>
                            </li>
                        </ul>

                        <div class="tab-content" id="resultTabsContent">
                            <div class="tab-pane fade show active" id="data-lama" role="tabpanel" aria-labelledby="data-lama-tab">
                                <div class="table-responsive mt-3">
                                    <table class="table table-bordered" id="tabelDataLama">
                                        <thead>
                                            <tr>
                                                <th>No</th>
                                                <th>Tanggal Pelayanan</th>
                                                <th>Jenis Data</th>
                                                <th>Aksi</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <!-- Data Lama akan dimuat di sini -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <!-- Hasil PA Tab -->
                            <div class="tab-pane fade show active" id="hasil-pa" role="tabpanel" aria-labelledby="hasil-pa-tab">
                                <div class="table-responsive mt-3">
                                    <!-- Sub Tabs for Sitologi, Histologi, Imunohistokimia, Molekuler -->
                                    <ul class="nav nav-tabs" id="paSubTabs" role="tablist">
                                        <li class="nav-item">
                                            <a class="nav-link active" id="sitologi-tab" data-toggle="tab" href="#sitologi" role="tab" aria-controls="sitologi" aria-selected="true">Sitologi</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="histologi-tab" data-toggle="tab" href="#histologi" role="tab" aria-controls="histologi" aria-selected="false">Histologi</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="imunohistokimia-tab" data-toggle="tab" href="#imunohistokimia" role="tab" aria-controls="imunohistokimia" aria-selected="false">Imunohistokimia</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="molekuler-tab" data-toggle="tab" href="#molekuler" role="tab" aria-controls="molekuler" aria-selected="false">Molekuler</a>
                                        </li>
                                    </ul>

                                    <div class="tab-content" id="paSubTabsContent">
                                        <!-- Sitologi Sub Tab Content -->
                                        <div class="tab-pane fade show active" id="sitologi" role="tabpanel" aria-labelledby="sitologi-tab">
                                            <div class="table-responsive mt-3">
                                                <table class="table table-bordered" id="tableSito">
                                                    <thead>
                                                        <tr>
                                                            <th>No</th>
                                                            <th>Nomor Lab</th>
                                                            <th>Tanggal Order</th>
                                                            <th>Aksi</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>

                                        <!-- Histologi Sub Tab Content -->
                                        <div class="tab-pane fade" id="histologi" role="tabpanel" aria-labelledby="histologi-tab">
                                            <div class="table-responsive mt-3">
                                                <table class="table table-bordered" id="tableHisto">
                                                    <thead>
                                                        <tr>
                                                            <th>No</th>
                                                            <th>Nomor Lab</th>
                                                            <th>Tanggal Order</th>
                                                            <th>Aksi</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>

                                        <!-- Imunohistokimia Sub Tab Content -->
                                        <div class="tab-pane fade" id="imunohistokimia" role="tabpanel" aria-labelledby="imunohistokimia-tab">
                                            <div class="table-responsive mt-3">
                                                <table class="table table-bordered" id="tableImuno">
                                                    <thead>
                                                        <tr>
                                                            <th>No</th>
                                                            <th>Nomor Lab</th>
                                                            <th>Tanggal Order</th>
                                                            <th>Aksi</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>

                                        <!-- Molekuler Sub Tab Content -->
                                        <div class="tab-pane fade" id="molekuler" role="tabpanel" aria-labelledby="molekuler-tab">
                                            <div class="table-responsive mt-3">
                                                <table class="table table-bordered" id="tableMole">
                                                    <thead>
                                                        <tr>
                                                            <th>No</th>
                                                            <th>Nomor Lab</th>
                                                            <th>Tanggal Order</th>
                                                            <th>Aksi</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div> <!-- End of Sub Tab Content -->
                                </div>
                            </div>

                            <!-- Hasil RAD Tab -->
                            <div class="tab-pane fade" id="hasil-rad" role="tabpanel" aria-labelledby="hasil-rad-tab">
                                <div class="table-responsive mt-3">
                                    <table class="table table-bordered tableRadiologi" id="tableRadiologi">
                                        <thead>
                                            <tr>
                                                <th>Tanggal Masuk</th>
                                                <th>Tindakan</th>
                                                <th>Lihat</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12 my-2">
                <div class="card">
                    <div class="card-body">
                        <h3 class="fw-bold" for="">Data Layanan</h3>

                        <div class="row">
                            <div class="form-group col-md-6">
                                <label for="tgl-layanan-farmasi">Tanggal Pelayanan</label>
                                <div class="input-group">
                                    <input type="date" class="form-control" id="tgl-layanan-farmasi" name="tgllayanan"
                                        value="<?php echo date('Y-m-d', strtotime($data_verified[0]['tanggal_pelayanan'])); ?>"
                                        disabled>
                                    <div class="input-group-append">
                                        <span class="input-group-text">
                                            <i class="fas fa-calendar"></i>
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group col-md-6">
                                <label for="tgl-jatuh-tempo">Tanggal Jatuh Tempo</label>
                                <div class="input-group">
                                    <input type="date" class="form-control" value="<?php echo date('Y-m-d', strtotime($data_verified[0]['tanggal_jatuh_tempo'])); ?>" id="tgl-jatuh-tempo" name="tglJatuhTempo" placeholder="Tanggal pelayanan" disabled>
                                    <div class="input-group-append">
                                        <span class="input-group-text">
                                            <i class="fas fa-calendar"></i>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group col-md-6">
                                <label for="tinggi-badan">Tinggi Badan (cm)</label>
                                <div class="input-group">
                                    <input type="number" step="0.01"
                                        class="form-control"
                                        id="tinggi-badan"
                                        name="tinggiBadan"
                                        placeholder="Masukkan tinggi badan"
                                        value="<?php echo !empty($data_verified[0]['tinggi_badan']) ? $data_verified[0]['tinggi_badan'] : ''; ?>"
                                        disabled >
                                    <div class="input-group-append">
                                        <span class="input-group-text">cm</span>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="berat-badan">Berat Badan (kg)</label>
                                <div class="input-group">
                                    <input type="number" step="0.01"
                                        class="form-control"
                                        id="berat-badan"
                                        name="beratBadan"
                                        placeholder="Masukkan berat badan"
                                        value="<?php echo !empty($data_verified[0]['berat_badan']) ? $data_verified[0]['berat_badan'] : ''; ?>"
                                        disabled>
                                    <div class="input-group-append">
                                        <span class="input-group-text">kg</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="fw-bold" for="data-emr-farmasi">Data Layanan</label>
                            <div class="card-header p-0 pt-1">
                                <ul class="nav nav-tabs" id="data-emr-farmasi" role="tablist">
                                    <li class="nav-item">
                                        <a class="nav-link active" id="tab-resep-farmasi" data-toggle="tab" href="#isi-tab-resep-farmasi" role="tab" aria-controls="isi-tab-resep-farmasi" aria-selected="true">
                                            Resep
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" id="tab-hasilpk-farmasi" data-toggle="tab" href="#isi-tab-hasilpk-farmasi" role="tab" aria-controls="isi-tab-hasilpk-farmasi" aria-selected="false">
                                            Hasil PK
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" id="tab-hasil-rad" data-toggle="tab" href="#isi-tab-hasil-rad" role="tab" aria-controls="isi-tab-hasil-rad" aria-selected="false">
                                            Hasil Radiologi
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <div class="card-body">
                                <div class="tab-content" id="data-emr-content-farmasi">
                                    <!-- Isi Tab Resep -->
                                    <div class="tab-pane fade show active" id="isi-tab-resep-farmasi" role="tabpanel" aria-labelledby="tab-resep-farmasi">
                                        <table id="tabel-resep-farmasi" class="table table-bordered table-striped table-hover" cellspacing="0" width="100%">
                                            <thead>
                                                <tr>
                                                    <th>No.</th>
                                                    <th>Unit Asal</th>
                                                    <th>Tujuan</th>
                                                    <th>Pemberi Resep</th>
                                                    <th>Waktu</th>
                                                    <th>Status</th>
                                                    <th>Aksi</th>
                                                </tr>
                                            </thead>
                                            <tbody></tbody>
                                        </table>
                                    </div>
                                    <!-- Akhir isi tab resep -->

                                    <!-- Mulai isi tab hasilpk -->
                                    <div class="tab-pane fade" id="isi-tab-hasilpk-farmasi" role="tabpanel" aria-labelledby="tab-hasilpk-farmasi">
                                        <table id="tabel-hasilpk-farmasi" class="table table-bordered table-striped table-hover" cellspacing="0" width="100%">
                                            <thead>
                                                <tr>
                                                    <th>Tanggal Masuk</th>
                                                    <th>Tindakan</th>
                                                    <th>Nokun</th>
                                                    <th>Aksi</th>
                                                </tr>
                                            </thead>
                                            <tbody></tbody>
                                        </table>
                                    </div>
                                    <!-- Akhir isi tab hasilpk -->
                                    <!-- awal tab radiologi 2 -->
                                    <div class="tab-pane fade" id="isi-tab-hasil-rad" role="tabpanel" aria-labelledby="tab-hasil-rad">
                                        <div class="table-responsive mt-3">
                                            <table class="table table-bordered tableRadiologi" id="tableRadiologi2">
                                                <thead>
                                                    <tr>
                                                        <th>Tanggal Masuk</th>
                                                        <th>Tindakan</th>
                                                        <th>Lihat</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Mulai lihat resep -->
<div class="modal fade" id="modal-resep-farmasi" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="myModalLabel">Detail Resep</h4>
                <button type="button" class="close close-modal" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body overflow-auto" id="lihat-resep-farmasi" style="max-height: calc(100vh - 200px);"></div>
        </div>
    </div>
</div>
<!-- Akhir lihat resep -->

<!-- Mulai lihat sitologi -->
<div class="modal fade" id="modal-sitologi-farmasi" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="myModalLabel">Hasil Sitologi</h4>
                <button type="button" class="close close-modal" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body overflow-auto" id="lihat-sitologi-farmasi" style="max-height: calc(100vh - 200px);"></div>
        </div>
    </div>
</div>
<!-- Akhir lihat sitologi -->


<div class="modal fade" id="modal-expertise-farmasi" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="myModalLabel">Hasil <em>Expertise</em></h4>
                <button type="button" class="close close-modal" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body overflow-auto" id="lihat-expertise-farmasi"></div>
        </div>
    </div>
</div>
<div class="modal fade" id="myModalPk" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">

</div>
<script>
    $(document).ready(function() {
        let norm = <?= json_encode($norm) ?>; // Use json_encode to safely pass PHP variable
        var id_layanan = <?= $id_layanan ?>;
        $('#tableDoc').DataTable({
            processing: true,
            serverSide: false,
            language: {
                "processing": 'Memuat Data...',
                "zeroRecords": "Data Tidak Ditemukan",
                "emptyTable": "Data Tidak Tersedia",
                "loadingRecords": "Harap Tunggu...",
                "paginate": {
                    "next": "Selanjutnya",
                    "previous": "Sebelumnya"
                },
                "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ Data",
                "infoEmpty": "Menampilkan 0 sampai 0 dari 0 Data",
                "search": "Cari:",
                "lengthMenu": "Tampilkan: _MENU_ Data",
                "infoFiltered": "(Disaring dari _MAX_ jumlah Data)",
            },
            ajax: {
                url: '<?= base_url('Farmasi/tableDocuments') ?>',
                type: 'GET',
                data: function(d) {
                    d.norm = norm; // Send the norm as part of the data
                }
            },
            columns: [{
                    data: 'file_category'
                },
                {
                    data: 'file_name'
                },
                {
                    data: 'created_at'
                },
                {
                    data: 'actions',
                    orderable: false
                } // Disable ordering for action column
            ],
            "pageLength": 10, // Set the default number of rows per page
            "lengthMenu": [
                [10, 25, 50, -1],
                [10, 25, 50, "All"]
            ] // Options for rows per page
        });
        bsCustomFileInput.init();
        let csrf_name = '<?= $csrf_token ?>';
        let csrf_hash = '<?= $csrf_hash ?>';
        let noRM = $('#no-rm-farmasi').val();

        if (noRM) {
            tabelInputFarmasi(noRM);
        } else {
            console.log("NoRM tidak ditemukan"); // Debugging
        }

        const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000
        });

        // Mulai tabel farmasi
        function tabelInputFarmasi(noRM) {
            // Mulai tabel resep
            $('#tabel-resep-farmasi').DataTable({
                responsive: true,
                order: [4, 'desc'],
                language: {
                    "processing": 'Memuat Data...',
                    "zeroRecords": "Data Tidak Ditemukan",
                    "emptyTable": "Data Tidak Tersedia",
                    "loadingRecords": "Harap Tunggu...",
                    "paginate": {
                        "next": "Selanjutnya",
                        "previous": "Sebelumnya"
                    },
                    "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ Data",
                    "infoEmpty": "Menampilkan 0 sampai 0 dari 0 Data",
                    "search": "Cari:",
                    "lengthMenu": "Tampilkan: _MENU_ Data",
                    "infoFiltered": "(Disaring dari _MAX_ jumlah Data)",
                },
                clear: true,
                destroy: true,
                processing: true,
                serverSide: true,
                iDisplayLength: 10,
                search: {
                    regex: true
                },

                // Mulai ambil data
                ajax: {
                    url: "<?= base_url('Farmasi/tabelResepBaru') ?>",
                    type: 'POST',
                    data: {
                        [csrf_name]: csrf_hash,
                        view_only: 1,
                        id_layanan: id_layanan,
                        norm: noRM,
                    }
                },
                // Akhir ambil data

                // Mulai pendefinisian kolom
                columnDefs: [{
                    targets: [0, 6],
                    orderable: false,
                }],
                // Akhir pendefinisian kolom
            });
            // Akhir tabel resep

            // Mulai tabel sitologi
            $('#tabel-hasilpk-farmasi').DataTable({
                responsive: true,
                serverSide: true,
                language: {
                    "processing": 'Memuat Data...',
                    "zeroRecords": "Data Tidak Ditemukan",
                    "emptyTable": "Data Tidak Tersedia",
                    "loadingRecords": "Harap Tunggu...",
                    "paginate": {
                        "next": "Selanjutnya",
                        "previous": "Sebelumnya"
                    },
                    "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ Data",
                    "infoEmpty": "Menampilkan 0 sampai 0 dari 0 Data",
                    "search": "Cari:",
                    "lengthMenu": "Tampilkan: _MENU_ Data",
                    "infoFiltered": "(Disaring dari _MAX_ jumlah Data)",
                },
                columns: [ // Checkbox sudah dirender di controller
                    {
                        "data": "masuk"
                    }, // Tanggal Masuk
                    {
                        "data": "tindakan"
                    },
                    {
                        "data": "nokun"
                    },
                    {
                        "data": "aksi"
                    } // Tombol Aksi sudah dirender di controller
                ],
                ajax: {
                    url: "<?= base_url('Farmasi/tablePK') ?>",
                    type: 'POST',
                    data: {
                        [csrf_name]: csrf_hash,
                        norm: noRM,
                        view_only: 1,
                        id_layanan: id_layanan
                    }
                }
            });
        };
        // Akhir tabel farmasi
        $('#tabelDataLama').DataTable({
            responsive: true,
            language: {
                url: "<?= base_url('assets/plugins/dataTables/id.json') ?>"
            },
            clear: true,
            destroy: true,
            processing: true,
            serverSide: true,

            // Performance optimizations
            deferRender: true,
            pageLength: 10, // Optimal page size for performance
            lengthMenu: [5, 10, 25, 50], // Reduced options for better performance
            search: {
                regex: true
            },

            // Optimize DOM manipulation
            dom: '<"row"<"col-sm-6"l><"col-sm-6"f>>' +
                 '<"row"<"col-sm-12"tr>>' +
                 '<"row"<"col-sm-5"i><"col-sm-7"p>>',

            // Fetching data via AJAX with optimizations
            ajax: {
                url: "<?= base_url('Farmasi/tableOldData') ?>",
                type: 'POST',
                datatype: 'json',
                data: {
                    [csrf_name]: csrf_hash,
                    nomorMr: <?= $data_pasien->norm ?>,
                    viewOnly: 1
                },
                // Add caching and compression
                cache: true,
                beforeSend: function(xhr) {
                    xhr.setRequestHeader('Accept-Encoding', 'gzip, deflate');
                },
                dataSrc: function(jsonData) {
                    return jsonData.data;
                }
            },

            // Define columns with optimizations
            columnDefs: [{
                targets: [0, 3],
                orderable: false,
            }],

            // Performance callbacks
            drawCallback: function(settings) {
                // Minimize DOM operations in draw callback
                $('[data-toggle="tooltip"]').tooltip();
            }
        });

        // Mulai lihat resep
        $(document).on('click', '.tbl-resep-farmasi', function() {
            $.ajax({
                type: 'POST',
                url: "<?= base_url('Farmasi/lihatResep') ?>",
                data: {
                    [csrf_name]: csrf_hash,
                    nomor: $(this).data('id')
                },
                success: function(data) {
                    $('#lihat-resep-farmasi').html(data);
                }
            });
        });

        $('#tabel-resep-farmasi').on('click', '.cetakResep', function() {
            var nokun = $(this).attr('data-id');
            window.requestPrint2({
                NAME: 'layanan.farmasi.CetakResepObat',
                TYPE: 'Pdf',
                EXT: 'Pdf',
                PARAMETER: {
                    PNOMOR: nokun
                },
                REQUEST_FOR_PRINT: false,
                PRINT_NAME: 'CetakResepFarmasi',
                CONNECTION_NUMBER: 0,
                COPIES: 1,
                id: "data.model.RequestReport-2"
            });
        });
        // Akhir lihat resep

        // Mulai lihat sitologi
        $(document).on('click', '.tbl-sitologi-farmasi', function() {
            $.ajax({
                type: 'POST',
                url: "<?= base_url('PA/lihatSitologi') ?>",
                data: {
                    [csrf_name]: csrf_hash,
                    no_lab: $(this).data('lab')
                },
                success: function(data) {
                    $('#lihat-sitologi-farmasi').html(data);
                }
            });
        });

        $('#submitFormulir').on('click', function(e) {
            e.preventDefault(); // Prevent the default form submission

            // Get the values of the date fields
            var tglLayanan = $('#tgl-layanan-farmasi').val();
            var tglJatuhTempo = $('#tgl-jatuh-tempo').val();

            // Check if either date field is empty
            if (!tglLayanan || !tglJatuhTempo) {
                // Show SweetAlert notification
                Swal.fire({
                    title: 'Peringatan!',
                    text: 'Harap isi semua kolom tanggal.',
                    icon: 'warning',
                    confirmButtonText: 'OK'
                });
                return; // Stop the function if validation fails
            }

            // Check if any checkbox is selected
            if ($('input[name="input_resep[]"]:checked').length === 0) {
                // Show SweetAlert notification for checkboxes
                Swal.fire({
                    title: 'Peringatan!',
                    text: 'Harap pilih setidaknya satu resep.',
                    icon: 'warning',
                    confirmButtonText: 'OK'
                });
                return; // Stop the function if validation fails
            }

            // Collect form data
            var formData = new FormData($('#formulirFarmasi')[0]);
            // Add CSRF token to the form data
            formData.append('<?= $csrf_token ?>', '<?= $csrf_hash ?>');

            // Show loading animation
            Swal.fire({
                title: 'Sedang memproses...',
                html: 'Silakan tunggu sebentar.',
                didOpen: () => {
                    Swal.showLoading();
                },
                showConfirmButton: false // Hide confirm button
            });

            // Send form data using AJAX
            $.ajax({
                url: '<?= base_url('Farmasi/submitFormulir') ?>',
                type: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                success: function(response) {
                    // Parse the JSON response
                    var result = JSON.parse(response);

                    // Close loading animation
                    Swal.close();

                    // Check if submission is successful
                    if (result.status === 'success') {
                        Swal.fire({
                            title: 'Sukses!',
                            text: result.message,
                            icon: 'success',
                            confirmButtonText: 'OK'
                        }).then((result) => {
                            window.location.href = '<?= base_url('Farmasi/data') ?>';
                        });
                    } else {
                        // Show error alert
                        Swal.fire({
                            title: 'Error!',
                            text: result.message,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function(xhr, status, error) {
                    // Close loading animation
                    Swal.close();

                    // Show error alert if something goes wrong
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan saat mengirimkan data.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        });


        var mr = <?= $norm ?>;

        //datatable hasil sito
        $('#tableSito').DataTable().clear();
        $('#tableSito').DataTable().destroy();
        $('#tableSito').DataTable({
            "responsive": true,
            "pageLength": 10,
            "processing": true,
            "serverSide": true,
            "bLengthChange": false,
            "ordering": false,
            "filter": false,
            "order": [],
            "language": {
                "processing": 'Memuat Data...',
                "zeroRecords": "Data Tidak Ditemukan",
                "emptyTable": "Data Tidak Tersedia",
                "loadingRecords": "Harap Tunggu...",
                "paginate": {
                    "next": "Selanjutnya",
                    "previous": "Sebelumnya"
                },
                "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ Data",
                "infoEmpty": "Menampilkan 0 sampai 0 dari 0 Data",
                "search": "Cari:",
                "lengthMenu": "Tampilkan: _MENU_ Data",
                "infoFiltered": "(Disaring dari _MAX_ jumlah Data)",
            },

            lengthMenu: [
                [10, 20, 30, 40, 50],
                [10, 20, 30, 40, 50]
            ],
            ajax: {
                url: '<?php echo base_url('farmasi/get_data_sito') ?>',
                type: 'POST',
                data: {
                    mr: mr,
                    view_only: 1,
                    [csrf_name]: csrf_hash
                },
            },
        });

        //datatable hasil histo
        $('#tableHisto').DataTable().clear();
        $('#tableHisto').DataTable().destroy();
        $('#tableHisto').DataTable({
            "responsive": true,
            "pageLength": 10,
            "processing": true,
            "serverSide": true,
            "bLengthChange": false,
            "ordering": false,
            "filter": false,
            "order": [],
            "language": {
                "processing": 'Memuat Data...',
                "zeroRecords": "Data Tidak Ditemukan",
                "emptyTable": "Data Tidak Tersedia",
                "loadingRecords": "Harap Tunggu...",
                "paginate": {
                    "next": "Selanjutnya",
                    "previous": "Sebelumnya"
                },
                "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ Data",
                "infoEmpty": "Menampilkan 0 sampai 0 dari 0 Data",
                "search": "Cari:",
                "lengthMenu": "Tampilkan: _MENU_ Data",
                "infoFiltered": "(Disaring dari _MAX_ jumlah Data)",
            },

            lengthMenu: [
                [10, 20, 30, 40, 50],
                [10, 20, 30, 40, 50]
            ],
            ajax: {
                url: '<?php echo base_url('farmasi/get_data_histo') ?>',
                type: 'POST',
                data: {
                    mr: mr,
                    view_only: 1,
                    [csrf_name]: csrf_hash
                },
            },
        });

        //datatable hasil imuno
        $('#tableImuno').DataTable().clear();
        $('#tableImuno').DataTable().destroy();
        $('#tableImuno').DataTable({
            "responsive": true,
            "pageLength": 10,
            "processing": true,
            "serverSide": true,
            "bLengthChange": false,
            "ordering": false,
            "filter": false,
            "order": [],
            "language": {
                "processing": 'Memuat Data...',
                "zeroRecords": "Data Tidak Ditemukan",
                "emptyTable": "Data Tidak Tersedia",
                "loadingRecords": "Harap Tunggu...",
                "paginate": {
                    "next": "Selanjutnya",
                    "previous": "Sebelumnya"
                },
                "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ Data",
                "infoEmpty": "Menampilkan 0 sampai 0 dari 0 Data",
                "search": "Cari:",
                "lengthMenu": "Tampilkan: _MENU_ Data",
                "infoFiltered": "(Disaring dari _MAX_ jumlah Data)",
            },

            lengthMenu: [
                [10, 20, 30, 40, 50],
                [10, 20, 30, 40, 50]
            ],
            ajax: {
                url: '<?php echo base_url('farmasi/get_data_imuno') ?>',
                type: 'POST',
                data: {
                    mr: mr,
                    view_only: 1,
                    [csrf_name]: csrf_hash
                },
            },
        });

        //datatable Molekuler
        $('#tableMole').DataTable().clear();
        $('#tableMole').DataTable().destroy();
        $('#tableMole').DataTable({
            "responsive": true,
            "pageLength": 10,
            "processing": true,
            "serverSide": true,
            "bLengthChange": false,
            "ordering": false,
            "filter": false,
            "order": [],
            "language": {
                "processing": 'Memuat Data...',
                "zeroRecords": "Data Tidak Ditemukan",
                "emptyTable": "Data Tidak Tersedia",
                "loadingRecords": "Harap Tunggu...",
                "paginate": {
                    "next": "Selanjutnya",
                    "previous": "Sebelumnya"
                },
                "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ Data",
                "infoEmpty": "Menampilkan 0 sampai 0 dari 0 Data",
                "search": "Cari:",
                "lengthMenu": "Tampilkan: _MENU_ Data",
                "infoFiltered": "(Disaring dari _MAX_ jumlah Data)",
            },

            lengthMenu: [
                [10, 20, 30, 40, 50],
                [10, 20, 30, 40, 50]
            ],
            ajax: {
                url: '<?php echo base_url('farmasi/get_data_mole') ?>',
                type: 'POST',
                data: {
                    mr: mr,
                    view_only: 1,
                    [csrf_name]: csrf_hash
                },
            },
        });

        $('#tableSito').on('click', '.cetakSito', function() {
            var id = $(this).attr('data');

            window.requestPrint({
                NAME: "layanan.CetakHasilPaSitologi",
                TYPE: 'Pdf', //Word
                EXT: 'pdf', //docs
                PARAMETER: {
                    PID: id,
                },
                REQUEST_FOR_PRINT: false, //true = print lansung false = view
                PRINT_NAME: "CetakPatologi",
                CONNECTION_NUMBER: 0,
                COPIES: 1,
            });
        });
        $('#tableHisto').on('click', '.cetakHisto', function() {
            var id = $(this).attr('data');

            window.requestPrint({
                NAME: "layanan.CetakHasilPa",
                TYPE: 'Pdf', //Word
                EXT: 'pdf', //docs
                PARAMETER: {
                    PID: id,
                },
                REQUEST_FOR_PRINT: false, //true = print lansung false = view
                PRINT_NAME: "CetakPatologi",
                CONNECTION_NUMBER: 0,
                COPIES: 1,
            });
        });

        $('#tableImuno').on('click', '.cetakImuno', function() {
            var id = $(this).attr('data');

            window.requestPrint({
                NAME: "layanan.CetakHasilPaIHK",
                TYPE: 'Pdf', //Word
                EXT: 'pdf', //docs
                PARAMETER: {
                    PID: id,
                },
                REQUEST_FOR_PRINT: false, //true = print lansung false = view
                PRINT_NAME: "CetakPatologi",
                CONNECTION_NUMBER: 0,
                COPIES: 1,
            });
        });



        $('#tableMole').on('click', '.cetakMole', function() {
            var id = $(this).attr('data');
            var jenis = $(this).attr('jenis');
            var desk = $(this).attr('desk');

            if (jenis == 1 || jenis == 4 || jenis == 5 || jenis == 6 || jenis == 7) {
                var link = 'layanan.Egfr';
            } else if (jenis == 2 || jenis == 8) {
                var link = 'layanan.HpvGenotyping';
            } else if (jenis == 3) {
                var link = 'layanan.HpvDna';
            }

            window.requestPrint({
                NAME: link,
                TYPE: 'Pdf', //Word
                EXT: 'pdf', //docs
                PARAMETER: {
                    IDPATMOL: id,
                    JENIS: jenis
                },
                REQUEST_FOR_PRINT: false, //true = print lansung false = view
                PRINT_NAME: "CetakPatologi",
                CONNECTION_NUMBER: 0,
                COPIES: 1,
            });

        });

        $('#tableRadiologi').DataTable({
            responsive: true,
            order: [1, 'desc'],
            language: {
                "processing": 'Memuat Data...',
                "zeroRecords": "Data Tidak Ditemukan",
                "emptyTable": "Data Tidak Tersedia",
                "loadingRecords": "Harap Tunggu...",
                "paginate": {
                    "next": "Selanjutnya",
                    "previous": "Sebelumnya"
                },
                "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ Data",
                "infoEmpty": "Menampilkan 0 sampai 0 dari 0 Data",
                "search": "Cari:",
                "lengthMenu": "Tampilkan: _MENU_ Data",
                "infoFiltered": "(Disaring dari _MAX_ jumlah Data)",
            },

            clear: true,
            destroy: true,
            processing: true,
            serverSide: true,
            iDisplayLength: 10,
            search: {
                regex: true
            },

            // Mulai ambil data
            ajax: {
                url: "<?= base_url('Farmasi/tableRadiologi') ?>",
                type: 'POST',
                data: {
                    [csrf_name]: csrf_hash,
                    norm: mr,
                    part: 1,
                    view_only: 1
                }
            },
            // Akhir ambil data

            // Mulai pendefinisian kolom
            // columnDefs: [{
            //     targets: [0, 3],
            //     orderable: false,
            // }],
            // Akhir pendefinisian kolom
        });
        $('#tableRadiologi2').DataTable({
            responsive: true,
            order: [1, 'desc'],
            language: {
                "processing": 'Memuat Data...',
                "zeroRecords": "Data Tidak Ditemukan",
                "emptyTable": "Data Tidak Tersedia",
                "loadingRecords": "Harap Tunggu...",
                "paginate": {
                    "next": "Selanjutnya",
                    "previous": "Sebelumnya"
                },
                "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ Data",
                "infoEmpty": "Menampilkan 0 sampai 0 dari 0 Data",
                "search": "Cari:",
                "lengthMenu": "Tampilkan: _MENU_ Data",
                "infoFiltered": "(Disaring dari _MAX_ jumlah Data)",
            },

            clear: true,
            destroy: true,
            processing: true,
            serverSide: true,
            iDisplayLength: 10,
            search: {
                regex: true
            },

            // Mulai ambil data
            ajax: {
                url: "<?= base_url('Farmasi/tableRadiologi') ?>",
                type: 'POST',
                data: {
                    [csrf_name]: csrf_hash,
                    norm: mr,
                    part: 2,
                    id_layanan: id_layanan,
                    view_only: 1
                }
            },
            // Akhir ambil data


            // Akhir pendefinisian kolom
        });

        $('.tableRadiologi').on('click', '.btn-cetak', function() {
            let id = $(this).data('id');

            window.requestPrint({
                NAME: 'layanan.CetakHasilRadCop',
                TYPE: 'Pdf', // Word
                EXT: 'pdf', // docs
                PARAMETER: {
                    PTINDAKAN: `${id}`, // backtick buat jadiin string
                },
                REQUEST_FOR_PRINT: false, // true = print lansung false = view
                PRINT_NAME: 'CetakRadiologi',
                CONNECTION_NUMBER: 0,
                COPIES: 1,
            });
        });
        $('.tableRadiologi').on('click', '.tbl-expertise-farmasi', function() {
            $.ajax({
                type: 'POST',
                url: "<?= base_url('Radiologi/lihatExpertise') ?>",
                data: {
                    [csrf_name]: csrf_hash,
                    id: $(this).data('tindakan')
                },
                success: function(data) {
                    $('#lihat-expertise-farmasi').html(data);
                }
            });
        });

        $(document).on('click', '.viewPk', function() {
            var nokun = $(this).data('id');
            $.ajax({
                type: 'POST',
                url: "<?php echo base_url('farmasi/view_pk') ?>",
                data: {
                    [csrf_name]: csrf_hash,
                    nokun: nokun
                },
                success: function(data) {
                    $('#myModalPk').html(data);
                    $('#myModalPk').modal('show');
                }
            });
        });
    });
</script>