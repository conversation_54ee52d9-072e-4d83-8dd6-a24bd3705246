<style>
    /* Styled Checkbox */
    .styled-checkbox {
        position: relative;
        appearance: none;
        width: 20px;
        height: 20px;
        background-color: #f1f1f1;
        border: 2px solid #ccc;
        border-radius: 4px;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    /* On hover, slightly enlarge the checkbox */
    .styled-checkbox:hover {
        transform: scale(1.1);
    }

    /* Add checkmark animation */
    .styled-checkbox:checked {
        background-color: #4CAF50;
        border-color: #4CAF50;
        animation: checkboxAnimation 0.3s ease forwards;
    }

    /* Checkmark and its animation */
    .styled-checkbox:checked::after {
        content: '';
        position: absolute;
        left: 6px;
        top: 2px;
        width: 6px;
        height: 12px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
    }

    /* Animation Keyframes */
    @keyframes checkboxAnimation {
        0% {
            transform: scale(0);
        }

        100% {
            transform: scale(1);
        }
    }
</style>
<link rel="stylesheet" href="<?= base_url('assets/plugins/bs-stepper/css/bs-stepper.min.css') ?>">
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-dark"><?= $header ? $header : null ?></h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('farmasi/data') ?>">Data Farmasi</a>
                    </li>
                    <li class="breadcrumb-item active">
                        <a href="<?= base_url('Farmasi/formData') ?>">Input Data</a>
                    </li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Content -->
<div class="content">
    <form id="formulirFarmasi">
        <div class="container-fluid">
            <div class="row">
                <!-- Patient Information Form (Nomor Rekam Medis, BPJS, Nama Pasien) -->
                <div class="col-sm-12 my-2">
                    <div class="card">
                        <div class="card-body">
                            <div class="form-group row">
                                <!-- Nomor Rekam Medis -->
                                <div class="col-md-3">
                                    <label for="no-rm-farmasi">Nomor Rekam Medis</label>
                                    <input type="text" class="form-control" id="no-rm-farmasi" name="norm"
                                        value="<?= isset($data_pasien->norm) ? $data_pasien->norm : '' ?>"
                                        placeholder="Nomor Rekam Medis" maxlength="6" readonly>
                                </div>
                                <!-- Nomor Kartu BPJS -->
                                <div class="col-md-4">
                                    <label for="no-bpjs-farmasi">Nomor Kartu BPJS</label>
                                    <input type="text" class="form-control" id="no-bpjs-farmasi" name="nobpjs"
                                        value="<?= isset($data_pasien->no_bpjs) ? $data_pasien->no_bpjs : '' ?>"
                                        placeholder="Nomor BPJS atau Jaminan" readonly>
                                </div>
                                <!-- Nama Pasien -->
                                <div class="col-md-5">
                                    <label for="nama-pasien-farmasi">Nama Pasien</label>
                                    <input type="text" class="form-control" id="nama-pasien-farmasi" name="nama"
                                        value="<?= isset($data_pasien->nama_pasien) ? $data_pasien->nama_pasien : '' ?>"
                                        placeholder="Nama Pasien" readonly>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if ($file_exist) : ?>
                    <div class="col-sm-12 my-2">
                        <div class="card">
                            <div class="card-body">
                                <h3 class="fw-bold">Dokumen Verifikasi Awal Sebelumnya</h3>
                                <div class="row">
                                    <div class="table-responsive mt-3">
                                        <table class="table table-bordered tableDoc" id="tableDoc">
                                            <thead>
                                                <tr>
                                                    <th>Jenis Dokumen</th>
                                                    <th>Nama File</th>
                                                    <th>Tanggal Upload</th>
                                                    <th>Aksi</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <script>
                        $(document).ready(function() {
                            let norm = <?= json_encode($norm) ?>; // Use json_encode to safely pass PHP variable

                            $('#tableDoc').DataTable({
                                processing: true,
                                serverSide: false,
                                language: {
                                    "processing": 'Memuat Data...',
                                    "zeroRecords": "Data Tidak Ditemukan",
                                    "emptyTable": "Data Tidak Tersedia",
                                    "loadingRecords": "Harap Tunggu...",
                                    "paginate": {
                                        "next": "Selanjutnya",
                                        "previous": "Sebelumnya"
                                    },
                                    "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ Data",
                                    "infoEmpty": "Menampilkan 0 sampai 0 dari 0 Data",
                                    "search": "Cari:",
                                    "lengthMenu": "Tampilkan: _MENU_ Data",
                                    "infoFiltered": "(Disaring dari _MAX_ jumlah Data)",
                                },
                                ajax: {
                                    url: '<?= base_url('Farmasi/tableDocuments') ?>',
                                    type: 'GET',
                                    data: function(d) {
                                        d.norm = norm; // Send the norm as part of the data
                                    }
                                },
                                columns: [{
                                        data: 'file_category',
                                        searchBuilder: {
                                            defaultCondition: 'exact'
                                        }
                                    },
                                    {
                                        data: 'file_name',
                                        searchBuilder: {
                                            defaultCondition: 'exact'
                                        }
                                    },
                                    {
                                        data: 'created_at',
                                        render: function(data, type, row) {
                                            if (type === 'display' || type === 'filter') {
                                                return data;
                                            }
                                            if (type === 'sort') {
                                                var parts = data.split(' ');
                                                var dateParts = parts[0].split('/');
                                                return dateParts[2] + dateParts[1] + dateParts[0] + parts[1].replace(/:/g, '');
                                            }
                                            return data;
                                        }
                                    },
                                    {
                                        data: 'actions',
                                        orderable: false
                                    } // Disable ordering for action column
                                ],
                                "pageLength": 10, // Set the default number of rows per page
                                "lengthMenu": [
                                    [10, 25, 50, -1],
                                    [10, 25, 50, "All"]
                                ] // Options for rows per page
                            });
                        });
                    </script>


                <?php endif; ?>

                <!-- Data Verifikasi Awal and Tabs (HASIL PA & HASIL RAD) -->
                <div class="col-sm-12 my-2">
                    <div class="card">
                        <div class="card-body">
                            <div class="bs-stepper">
                                <div class="bs-stepper-header" role="tablist">
                                    <!-- your steps here -->
                                    <div class="step" data-target="#logins-part">
                                        <button type="button" class="step-trigger" role="tab" aria-controls="logins-part" id="logins-part-trigger">
                                            <span class="bs-stepper-circle">1</span>
                                            <span class="bs-stepper-label">Data Verifikasi Awal</span>
                                        </button>
                                    </div>
                                    <div class="line"></div>
                                    <div class="step" data-target="#information-part">
                                        <button type="button" class="step-trigger" role="tab" aria-controls="information-part" id="information-part-trigger">
                                            <span class="bs-stepper-circle">2</span>
                                            <span class="bs-stepper-label">Data Layanan</span>
                                        </button>
                                    </div>
                                </div>
                                <div class="bs-stepper-content">
                                    <!-- your steps content here -->
                                    <div id="logins-part" class="content" role="tabpanel" aria-labelledby="logins-part-trigger">
                                        <label class="fw-bold" for="">Data Verifikasi Awal</label>
                                        <div class="my-2">
                                            <span class="text-warning">Mengunggah dokumen dibawah akan menambah file, tidak mengupdate atau menghilangkan dokumen yang lama</span>
                                        </div>
                                        <div class="row">
                                            <!-- Kolom Kiri -->
                                            <div class="col-md-6">
                                                <!-- File Upload JPOK -->
                                                <div class="form-group">
                                                    <label for="jpok-farmasi">JPOK</label>
                                                    <div class="custom-file">
                                                        <input type="file" class="custom-file-input" id="jpok-farmasi" name="jpok" accept=".doc, .docx, .pdf, .jpg, .jpeg, .png">
                                                        <label class="custom-file-label" for="jpok-farmasi">Pilih berkas ....</label>
                                                    </div>
                                                    <small id="help-jpok-farmasi" class="form-text text-yellow">
                                                        File yang didukung: .doc, .docx, .pdf, .jpg, .jpeg, .png
                                                    </small>
                                                </div>

                                                <!-- File Upload IHK Eksternal -->
                                                <div class="form-group">
                                                    <label for="externIhk">IHK Eksternal</label>
                                                    <div class="custom-file">
                                                        <input type="file" class="custom-file-input" id="externIhk" name="extern_ihk" accept=".doc, .docx, .pdf, .jpg, .jpeg, .png">
                                                        <label class="custom-file-label" for="externIhk">Pilih berkas ....</label>
                                                    </div>
                                                    <small id="help-externIhk" class="form-text text-yellow">
                                                        File yang didukung: .doc, .docx, .pdf, .jpg, .jpeg, .png
                                                    </small>
                                                </div>

                                                <!-- File Upload Radiologi Eksternal -->
                                                <div class="form-group">
                                                    <label for="externRadiologi">Radiologi Eksternal</label>
                                                    <div class="custom-file">
                                                        <input type="file" class="custom-file-input" id="externRadiologi" name="extern_radiologi" accept=".doc, .docx, .pdf, .jpg, .jpeg, .png">
                                                        <label class="custom-file-label" for="externRadiologi">Pilih berkas ....</label>
                                                    </div>
                                                    <small id="help-externRadiologi" class="form-text text-yellow">
                                                        File yang didukung: .doc, .docx, .pdf, .jpg, .jpeg, .png
                                                    </small>
                                                </div>
                                            </div>

                                            <!-- Kolom Kanan -->
                                            <div class="col-md-6">
                                                <!-- File Upload PA Eksternal -->
                                                <div class="form-group">
                                                    <label for="externPa">PA Eksternal</label>
                                                    <div class="custom-file">
                                                        <input type="file" class="custom-file-input" id="externPa" name="extern_pa" accept=".doc, .docx, .pdf, .jpg, .jpeg, .png">
                                                        <label class="custom-file-label" for="externPa">Pilih berkas ....</label>
                                                    </div>
                                                    <small id="help-externPa" class="form-text text-yellow">
                                                        File yang didukung: .doc, .docx, .pdf, .jpg, .jpeg, .png
                                                    </small>
                                                </div>

                                                <!-- File Upload BMP Eksternal -->
                                                <div class="form-group">
                                                    <label for="externBmp">BMP Eksternal</label>
                                                    <div class="custom-file">
                                                        <input type="file" class="custom-file-input" id="externBmp" name="extern_bmp" accept=".doc, .docx, .pdf, .jpg, .jpeg, .png">
                                                        <label class="custom-file-label" for="externBmp">Pilih berkas ....</label>
                                                    </div>
                                                    <small id="help-externBmp" class="form-text text-yellow">
                                                        File yang didukung: .doc, .docx, .pdf, .jpg, .jpeg, .png
                                                    </small>
                                                </div>

                                                <!-- File Upload Dokumen Lainnya -->
                                                <div class="form-group">
                                                    <label for="other-docs">Dokumen Lainnya</label>
                                                    <div class="custom-file">
                                                        <input type="file" class="custom-file-input" id="other-docs" name="other_docs[]" accept=".doc, .docx, .pdf, .jpg, .jpeg, .png" multiple>
                                                        <label class="custom-file-label" for="other-docs">Pilih berkas ....</label>
                                                    </div>
                                                    <small id="help-other-docs" class="form-text text-yellow">
                                                        File yang didukung: .doc, .docx, .pdf, .jpg, .jpeg, .png
                                                    </small>
                                                </div>
                                            </div>
                                        </div>


                                        <!-- Label "Pilih Data Layanan" -->
                                        <label class="fw-bold" for="">Pilih Data Verifikasi Awal</label>

                                        <!-- Tabs for HASIL PA and HASIL RAD inside the same card -->

                                        <ul class="nav nav-tabs" id="resultTabs" role="tablist">
                                            <li class="nav-item">
                                                <a class="nav-link active" id="data-lama-tab" data-toggle="tab" href="#data-lama" role="tab" aria-controls="data-lama" aria-selected="true">DATA LAMA</a>
                                            </li>
                                            <li class="nav-item">
                                                <a class="nav-link" id="hasil-pa-tab" data-toggle="tab" href="#hasil-pa" role="tab" aria-controls="hasil-pa" aria-selected="false">HASIL PA</a>
                                            </li>
                                            <li class="nav-item">
                                                <a class="nav-link" id="hasil-rad-tab" data-toggle="tab" href="#hasil-rad" role="tab" aria-controls="hasil-rad" aria-selected="false">HASIL RADIOLOGI</a>
                                            </li>
                                        </ul>

                                        <div class="tab-content" id="resultTabsContent">
                                            <!-- DATA LAMA Tab Content -->
                                            <div class="tab-pane fade show active" id="data-lama" role="tabpanel" aria-labelledby="data-lama-tab">
                                                <div class="table-responsive mt-3">
                                                    <table class="table table-bordered" id="tabelDataLama">
                                                        <thead>
                                                            <tr>
                                                                <th>No</th>
                                                                <th>Tanggal Pelayanan</th>
                                                                <th>Jenis Data</th>
                                                                <th>Aksi</th>
                                                                <th>Pilih</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <!-- Data Lama akan dimuat di sini -->
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>

                                            <!-- Hasil PA Tab -->
                                            <div class="tab-pane fade" id="hasil-pa" role="tabpanel" aria-labelledby="hasil-pa-tab">
                                                <!-- Konten Hasil PA -->
                                                <div class="table-responsive mt-3">
                                                    <ul class="nav nav-tabs" id="paSubTabs" role="tablist">
                                                        <li class="nav-item">
                                                            <a class="nav-link active" id="sitologi-tab" data-toggle="tab" href="#sitologi" role="tab" aria-controls="sitologi" aria-selected="true">Sitologi</a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="histologi-tab" data-toggle="tab" href="#histologi" role="tab" aria-controls="histologi" aria-selected="false">Histologi</a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="imunohistokimia-tab" data-toggle="tab" href="#imunohistokimia" role="tab" aria-controls="imunohistokimia" aria-selected="false">Imunohistokimia</a>
                                                        </li>
                                                        <li class="nav-item">
                                                            <a class="nav-link" id="molekuler-tab" data-toggle="tab" href="#molekuler" role="tab" aria-controls="molekuler" aria-selected="false">Molekuler</a>
                                                        </li>
                                                    </ul>

                                                    <div class="tab-content" id="paSubTabsContent">
                                                        <!-- Sitologi Sub Tab Content -->
                                                        <div class="tab-pane fade show active" id="sitologi" role="tabpanel" aria-labelledby="sitologi-tab">
                                                            <div class="table-responsive mt-3">
                                                                <table class="table table-bordered" id="tableSito">
                                                                    <thead>
                                                                        <tr>
                                                                            <th>No</th>
                                                                            <th>Nomor Lab</th>
                                                                            <th>Tanggal Order</th>
                                                                            <th>Aksi</th>
                                                                            <th>Pilih</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </div>
                                                        <!-- Other Sub Tabs -->


                                                        <!-- Histologi Sub Tab Content -->
                                                        <div class="tab-pane fade" id="histologi" role="tabpanel" aria-labelledby="histologi-tab">
                                                            <div class="table-responsive mt-3">
                                                                <table class="table table-bordered" id="tableHisto">
                                                                    <thead>
                                                                        <tr>
                                                                            <th>No</th>
                                                                            <th>Nomor Lab</th>
                                                                            <th>Tanggal Order</th>
                                                                            <th>Aksi</th>
                                                                            <th>Pilih</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </div>

                                                        <!-- Imunohistokimia Sub Tab Content -->
                                                        <div class="tab-pane fade" id="imunohistokimia" role="tabpanel" aria-labelledby="imunohistokimia-tab">
                                                            <div class="table-responsive mt-3">
                                                                <table class="table table-bordered" id="tableImuno">
                                                                    <thead>
                                                                        <tr>
                                                                            <th>No</th>
                                                                            <th>Nomor Lab</th>
                                                                            <th>Tanggal Order</th>
                                                                            <th>Aksi</th>
                                                                            <th>Pilih</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </div>

                                                        <!-- Molekuler Sub Tab Content -->
                                                        <div class="tab-pane fade" id="molekuler" role="tabpanel" aria-labelledby="molekuler-tab">
                                                            <div class="table-responsive mt-3">
                                                                <table class="table table-bordered" id="tableMole">
                                                                    <thead>
                                                                        <tr>
                                                                            <th>No</th>
                                                                            <th>Nomor Lab</th>
                                                                            <th>Tanggal Order</th>
                                                                            <th>Aksi</th>
                                                                            <th>Pilih</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Hasil RAD Tab -->
                                            <div class="tab-pane fade" id="hasil-rad" role="tabpanel" aria-labelledby="hasil-rad-tab">
                                                <div class="table-responsive mt-3">
                                                    <table class="table table-bordered tableRadiologi" id="tableRadiologi">
                                                        <thead>
                                                            <tr>
                                                                <th>Pilih</th>
                                                                <th>Tanggal Masuk</th>
                                                                <th>Tindakan</th>
                                                                <th>Lihat</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- LAST -->
                                        <div class="d-flex justify-content-end mt-4">
                                            <a id="nextStep" class="btn btn-primary" onclick="validateStep()">
                                                Lanjut Data Layanan <i class="fas fa-arrow-right"></i>
                                            </a>
                                        </div>
                                    </div>
                                    <div id="information-part" class="content" role="tabpanel" aria-labelledby="information-part-trigger">
                                        <label class="fw-bold" for="">Input Data Layanan</label>
                                        <div class="row">
                                            <div class="form-group col-md-6">
                                                <label for="tgl-layanan-farmasi">Tanggal Pelayanan</label>
                                                <div class="input-group">
                                                    <input type="date" class="form-control" id="tgl-layanan-farmasi" name="tgllayanan" placeholder="Tanggal pelayanan" required>
                                                    <div class="input-group-append">
                                                        <span class="input-group-text">
                                                            <i class="fas fa-calendar"></i>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group col-md-6">
                                                <label for="tgl-jatuh-tempo">Tanggal Jatuh Tempo</label>
                                                <div class="input-group">
                                                    <input type="date" class="form-control" id="tgl-jatuh-tempo" name="tglJatuhTempo" placeholder="Tanggal pelayanan" required>
                                                    <div class="input-group-append">
                                                        <span class="input-group-text">
                                                            <i class="fas fa-calendar"></i>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="form-group col-md-6">
                                                <label for="tinggi-badan">Tinggi Badan (cm)</label>
                                                <div class="input-group">
                                                    <input type="number" step="0.01" class="form-control" id="tinggi-badan" name="tinggiBadan" placeholder="Masukkan tinggi badan" required>
                                                    <div class="input-group-append">
                                                        <span class="input-group-text">cm</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group col-md-6">
                                                <label for="berat-badan">Berat Badan (kg)</label>
                                                <div class="input-group">
                                                    <input type="number" step="0.01" class="form-control" id="berat-badan" name="beratBadan" placeholder="Masukkan berat badan" required>
                                                    <div class="input-group-append">
                                                        <span class="input-group-text">kg</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="fw-bold" for="data-emr-farmasi">Pilih Data Layanan</label>
                                            <div class="card-header p-0 pt-1">
                                                <ul class="nav nav-tabs" id="data-emr-farmasi" role="tablist">
                                                    <li class="nav-item">
                                                        <a class="nav-link active" id="tab-resep-farmasi" data-toggle="tab" href="#isi-tab-resep-farmasi" role="tab" aria-controls="isi-tab-resep-farmasi" aria-selected="true">
                                                            Resep
                                                        </a>
                                                    </li>
                                                    <li class="nav-item">
                                                        <a class="nav-link" id="tab-hasilpk-farmasi" data-toggle="tab" href="#isi-tab-hasilpk-farmasi" role="tab" aria-controls="isi-tab-hasilpk-farmasi" aria-selected="false">
                                                            Hasil PK
                                                        </a>
                                                    </li>
                                                    <li class="nav-item">
                                                        <a class="nav-link" id="tab-hasil-rad" data-toggle="tab" href="#isi-tab-hasil-rad" role="tab" aria-controls="isi-tab-hasil-rad" aria-selected="false">
                                                            Hasil Radiologi
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                            <div class="card-body">
                                                <div class="tab-content" id="data-emr-content-farmasi">
                                                    <!-- Isi Tab Resep -->
                                                    <div class="tab-pane fade show active" id="isi-tab-resep-farmasi" role="tabpanel" aria-labelledby="tab-resep-farmasi">
                                                        <table id="tabel-resep-farmasi" class="table table-bordered table-striped table-hover" cellspacing="0" width="100%">
                                                            <thead>
                                                                <tr>
                                                                    <th>No.</th>
                                                                    <th>Unit Asal</th>
                                                                    <th>Tujuan</th>
                                                                    <th>SEP</th>
                                                                    <th>Pemberi Resep</th>
                                                                    <th>Waktu</th>
                                                                    <th>Status</th>
                                                                    <th>Aksi</th>
                                                                    <th>Pilih</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody></tbody>
                                                        </table>
                                                    </div>
                                                    <!-- Akhir isi tab resep -->

                                                    <!-- Mulai isi tab hasilpk -->
                                                    <div class="tab-pane fade" id="isi-tab-hasilpk-farmasi" role="tabpanel" aria-labelledby="tab-hasilpk-farmasi">
                                                        <table id="tabel-hasilpk-farmasi" class="table table-bordered table-striped table-hover" cellspacing="0" width="100%">
                                                            <thead>
                                                                <tr>
                                                                    <th>Pilih</th>
                                                                    <th>Tanggal Masuk</th>
                                                                    <th>Tindakan</th>
                                                                    <th>Nokun</th>
                                                                    <th>Aksi</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody></tbody>
                                                        </table>
                                                    </div>
                                                    <!-- Akhir isi tab hasilpk -->
                                                    <!-- awal tab radiologi 2 -->
                                                    <div class="tab-pane fade" id="isi-tab-hasil-rad" role="tabpanel" aria-labelledby="tab-hasil-rad">
                                                        <div class="table-responsive mt-3">
                                                            <table class="table table-bordered tableRadiologi" id="tableRadiologi2">
                                                                <thead>
                                                                    <tr>
                                                                        <th>Pilih</th>
                                                                        <th>Tanggal Masuk</th>
                                                                        <th>Tindakan</th>
                                                                        <th>Lihat</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <a class="btn btn-primary" onclick="stepper.previous()">
                                            <i class="fas fa-arrow-left"></i> Sebelumnya
                                        </a>
                                        <div class="d-flex justify-content-end mt-5">
                                            <button type="submit" id="submitFormulir" class="btn btn-success">Simpan Data</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <input type="hidden" name="user_exist" value="<?= $verif_exist ? 1 : 0; ?>">
    </form>
</div>

<!-- Mulai lihat resep -->
<div class="modal fade" id="modal-resep-farmasi" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="myModalLabel">Detail Resep</h4>
                <button type="button" class="close close-modal" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body overflow-auto" id="lihat-resep-farmasi" style="max-height: calc(100vh - 200px);"></div>
        </div>
    </div>
</div>
<!-- Akhir lihat resep -->

<!-- Mulai lihat sitologi -->
<div class="modal fade" id="modal-sitologi-farmasi" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="myModalLabel">Hasil Sitologi</h4>
                <button type="button" class="close close-modal" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body overflow-auto" id="lihat-sitologi-farmasi" style="max-height: calc(100vh - 200px);"></div>
        </div>
    </div>
</div>
<!-- Akhir lihat sitologi -->


<div class="modal fade" id="modal-expertise-farmasi" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" data-backdrop="static" data-keyboard="false">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="myModalLabel">Hasil <em>Expertise</em></h4>
                <button type="button" class="close close-modal" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body overflow-auto" id="lihat-expertise-farmasi"></div>
        </div>
    </div>
</div>
<div class="modal fade" id="myModalPk" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">

</div>
<script>
    function validateStep() {
        stepper.next();
    }
    document.addEventListener('DOMContentLoaded', function() {
        window.stepper = new Stepper(document.querySelector('.bs-stepper'))
    })
    // Declare global variables for checkbox states - moved outside document.ready to ensure global scope
    let selectedResep = [];
    let unselectedResep = [];
    let selectedPK = [];
    let unselectedPK = [];
    let selectedDataLama = [];
    let unselectedDataLama = [];
    let selectedSito = [];
    let unselectedSito = [];
    let selectedHisto = [];
    let unselectedHisto = [];
    let selectedImuno = [];
    let unselectedImuno = [];
    let selectedMole = [];
    let unselectedMole = [];
    let selectedRadiologi1 = [];
    let unselectedRadiologi1 = [];
    let selectedRadiologi2 = [];
    let unselectedRadiologi2 = [];

    // Global variables for CSRF and MR
    var mr = <?= $norm ?>;
    let csrf_name = '<?= $csrf_token ?>';
    let csrf_hash = '<?= $csrf_hash ?>';

    $(document).ready(function() {
        bsCustomFileInput.init();
        let noRM = $('#no-rm-farmasi').val();

        // Always initialize tables, even if noRM is empty initially
        // This ensures all variables are properly declared and DataTables are set up
        tabelInputFarmasi(noRM || '');

        const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000
        });

        // Optimized tabel farmasi
        function tabelInputFarmasi(noRM) {
            // Performance optimizations
            const tableConfig = {
                responsive: true,
                order: [4, 'desc'],
                stateSave: false, // Disable state save for better performance
                language: {
                    "processing": 'Memuat Data...',
                    "zeroRecords": "Data Tidak Ditemukan",
                    "emptyTable": "Data Tidak Tersedia",
                    "loadingRecords": "Harap Tunggu...",
                    "paginate": {
                        "next": "Selanjutnya",
                        "previous": "Sebelumnya"
                    },
                    "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ Data",
                    "infoEmpty": "Menampilkan 0 sampai 0 dari 0 Data",
                    "search": "Cari:",
                    "lengthMenu": "Tampilkan: _MENU_ Data",
                    "infoFiltered": "(Disaring dari _MAX_ jumlah Data)",
                },
                destroy: true,
                processing: true,
                serverSide: true,
                pageLength: 10, // Optimal page size
                lengthMenu: [5, 10, 25, 50], // Reduced options
                deferRender: true, // Improve performance
                search: {
                    regex: false // Disable regex for better performance
                },
                ajax: {
                    url: "<?= base_url('Farmasi/tabelResepBaru') ?>",
                    type: 'POST',
                    data: function(d) {
                        d[csrf_name] = csrf_hash;
                        d.norm = noRM || '';
                    },
                    error: function(xhr, error, thrown) {
                        console.error('DataTable Ajax Error:', error);
                    }
                },
                columnDefs: [{
                    targets: [0, 7],
                    orderable: false,
                }],
                // Optimized draw callback
                drawCallback: function() {
                    // Use more efficient selector and batch DOM operations
                    const checkboxes = this.api().table().container().querySelectorAll('.pilihResep');
                    checkboxes.forEach(function(checkbox) {
                        const id = checkbox.value;
                        // First check if user explicitly unchecked this item
                        if (unselectedResep.includes(id)) {
                            checkbox.checked = false;
                        }
                        // Then check if user selected this item or it was pre-selected
                        else if (selectedResep.includes(id)) {
                            checkbox.checked = true;
                        }
                        // For items not in either array, keep the server-provided state
                    });
                }
            };

            // Initialize table with optimized config
            let resepInitializedFromServer = false;

            // Initialize selectedResep with checked IDs from the first AJAX response
            $('#tabel-resep-farmasi').on('xhr.dt', function(e, settings, json, xhr) {
                if (json && json.data && !resepInitializedFromServer) {
                    json.data.forEach(function(row) {
                        if (row[8] && row[8].includes('checked')) {
                            const match = row[8].match(/value=['\"](\d+)['\"]/);
                            if (match && match[1]) {
                                selectedResep.push(match[1]);
                            }
                        }
                    });
                    resepInitializedFromServer = true;
                }
            });

            // Clear existing table if any
            if ($.fn.DataTable.isDataTable('#tabel-resep-farmasi')) {
                $('#tabel-resep-farmasi').DataTable().destroy();
            }

            const table = $('#tabel-resep-farmasi').DataTable(tableConfig);

            // Listen to checkbox changes
            $(document).on('change', '.pilihResep', function() {
                const id = $(this).val();
                if ($(this).is(':checked')) {
                    if (!selectedResep.includes(id)) {
                        selectedResep.push(id);
                    }
                    // Remove from unselected if it was there
                    unselectedResep = unselectedResep.filter(item => item !== id);
                } else {
                    if (!unselectedResep.includes(id)) {
                        unselectedResep.push(id);
                    }
                    // Remove from selected if it was there
                    selectedResep = selectedResep.filter(item => item !== id);
                }
            });

            let pkInitializedFromServer = false;

            // Initialize selectedPK with checked IDs from the first AJAX response
            $('#tabel-hasilpk-farmasi').on('xhr.dt', function(e, settings, json, xhr) {
                if (json && json.data && !pkInitializedFromServer) {
                    // Only initialize from server data on first load
                    json.data.forEach(function(row) {
                        if (row.pilih && row.pilih.includes('checked')) {
                            const match = row.pilih.match(/value=['\"](\d+)['\"]/);
                            if (match && match[1]) {
                                selectedPK.push(match[1]);
                            }
                        }
                    });
                    pkInitializedFromServer = true;
                }
            });

            // Optimized PK table configuration
            const pkTableConfig = {
                responsive: true,
                serverSide: true,
                pageLength: 10,
                lengthMenu: [5, 10, 25, 50],
                deferRender: true,
                search: {
                    regex: false
                },
                language: {
                    "processing": 'Memuat Data...',
                    "zeroRecords": "Data Tidak Ditemukan",
                    "emptyTable": "Data Tidak Tersedia",
                    "loadingRecords": "Harap Tunggu...",
                    "paginate": {
                        "next": "Selanjutnya",
                        "previous": "Sebelumnya"
                    },
                    "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ Data",
                    "infoEmpty": "Menampilkan 0 sampai 0 dari 0 Data",
                    "search": "Cari:",
                    "lengthMenu": "Tampilkan: _MENU_ Data",
                    "infoFiltered": "(Disaring dari _MAX_ jumlah Data)",
                },
                columns: [{
                        "data": "pilih"
                    },
                    {
                        "data": "masuk"
                    },
                    {
                        "data": "tindakan"
                    },
                    {
                        "data": "nokun"
                    },
                    {
                        "data": "aksi"
                    }
                ],
                ajax: {
                    url: "<?= base_url('Farmasi/tablePK') ?>",
                    type: 'POST',
                    data: function(d) {
                        d[csrf_name] = csrf_hash;
                        d.norm = noRM || '';
                    },
                    error: function(xhr, error, thrown) {
                        console.error('PK Table Ajax Error:', error);
                    }
                },
                drawCallback: function() {
                    // Optimized checkbox state restoration
                    const checkboxes = this.api().table().container().querySelectorAll('.pilihPK');
                    checkboxes.forEach(function(checkbox) {
                        const id = checkbox.value;
                        if (unselectedPK.includes(id)) {
                            checkbox.checked = false;
                        } else if (selectedPK.includes(id)) {
                            checkbox.checked = true;
                        }
                    });
                }
            };

            // Clear existing table if any
            if ($.fn.DataTable.isDataTable('#tabel-hasilpk-farmasi')) {
                $('#tabel-hasilpk-farmasi').DataTable().destroy();
            }

            const tablePK = $('#tabel-hasilpk-farmasi').DataTable(pkTableConfig);

            // Listen for checkbox changes
            $(document).on('change', '.pilihPK', function() {
                const id = $(this).val();
                if ($(this).is(':checked')) {
                    if (!selectedPK.includes(id)) {
                        selectedPK.push(id);
                    }
                    // Remove from unselected if it was there
                    unselectedPK = unselectedPK.filter(item => item !== id);
                } else {
                    if (!unselectedPK.includes(id)) {
                        unselectedPK.push(id);
                    }
                    // Remove from selected if it was there
                    selectedPK = selectedPK.filter(item => item !== id);
                }
            });

        };
        // Akhir tabel farmasi

        let dataLamaInitializedFromServer = false;

        // Initialize selectedDataLama with checked IDs from the first AJAX response
        $('#tabelDataLama').on('xhr.dt', function(e, settings, json, xhr) {
            if (json && json.data && !dataLamaInitializedFromServer) {
                // Only initialize from server data on first load
                json.data.forEach(function(row) {
                    // Check if the row contains a checked checkbox (assuming it's in the last column)
                    if (row[4] && row[4].includes('checked')) {
                        const match = row[4].match(/value=['\"](\d+)['\"]/);
                        if (match && match[1]) {
                            selectedDataLama.push(match[1]);
                        }
                    }
                });
                dataLamaInitializedFromServer = true;
            }
        });

        // Optimized Data Lama table configuration
        const dataLamaConfig = {
            responsive: true,
            language: {
                url: "<?= base_url('assets/plugins/dataTables/id.json') ?>"
            },
            destroy: true,
            processing: true,
            serverSide: true,
            pageLength: 5,
            lengthMenu: [5, 10, 25, 50], // Reduced options for better performance
            deferRender: true,
            search: {
                regex: false // Disable regex for better performance
            },
            ajax: {
                url: "<?= base_url('Farmasi/tableOldData') ?>",
                type: 'POST',
                datatype: 'json',
                data: {
                    [csrf_name]: csrf_hash,
                    nomorMr: <?= $data_pasien->norm ?>,
                },
                dataSrc: function(jsonData) {
                    return jsonData.data;
                },
                error: function(xhr, error, thrown) {
                    console.error('Data Lama Table Ajax Error:', error);
                }
            },
            drawCallback: function() {
                // Optimized checkbox state restoration
                const checkboxes = this.api().table().container().querySelectorAll('.inpOldTable');
                checkboxes.forEach(function(checkbox) {
                    const id = checkbox.value;
                    if (unselectedDataLama.includes(id)) {
                        checkbox.checked = false;
                    } else if (selectedDataLama.includes(id)) {
                        checkbox.checked = true;
                    }
                });
            },
            columnDefs: [{
                targets: [0, 3, 4],
                orderable: false,
            }],
        };

        // Clear existing table if any
        if ($.fn.DataTable.isDataTable('#tabelDataLama')) {
            $('#tabelDataLama').DataTable().destroy();
        }

        $('#tabelDataLama').DataTable(dataLamaConfig);

        $(document).on('change', '.inpOldTable', function() {
            const id = $(this).val();
            if ($(this).is(':checked')) {
                if (!selectedDataLama.includes(id)) {
                    selectedDataLama.push(id);
                }
                // Remove from unselected if it was there
                unselectedDataLama = unselectedDataLama.filter(item => item !== id);
            } else {
                if (!unselectedDataLama.includes(id)) {
                    unselectedDataLama.push(id);
                }
                // Remove from selected if it was there
                selectedDataLama = selectedDataLama.filter(item => item !== id);
            }
        });

        // Mulai lihat resep
        $(document).on('click', '.tbl-resep-farmasi', function() {
            $.ajax({
                type: 'POST',
                url: "<?= base_url('Farmasi/lihatResep') ?>",
                data: {
                    [csrf_name]: csrf_hash,
                    nomor: $(this).data('id')
                },
                success: function(data) {
                    $('#lihat-resep-farmasi').html(data);
                }
            });
        });

        $('#tabel-resep-farmasi').on('click', '.cetakResep', function() {
            var nokun = $(this).attr('data-id');
            window.requestPrint2({
                NAME: 'layanan.farmasi.CetakResepObat',
                TYPE: 'Pdf',
                EXT: 'Pdf',
                PARAMETER: {
                    PNOMOR: nokun
                },
                REQUEST_FOR_PRINT: false,
                PRINT_NAME: 'CetakResepFarmasi',
                CONNECTION_NUMBER: 0,
                COPIES: 1,
                id: "data.model.RequestReport-2"
            });
        });
        // Akhir lihat resep

        // Mulai lihat sitologi
        $(document).on('click', '.tbl-sitologi-farmasi', function() {
            $.ajax({
                type: 'POST',
                url: "<?= base_url('PA/lihatSitologi') ?>",
                data: {
                    [csrf_name]: csrf_hash,
                    no_lab: $(this).data('lab')
                },
                success: function(data) {
                    $('#lihat-sitologi-farmasi').html(data);
                }
            });
        });
        // Akhir lihat sitologi
    });
</script>
<script>
    $(document).ready(function() {
        <?php if ($verif_exist): ?>
            Swal.fire({
                title: 'Info',
                text: 'Pasien ini sudah pernah verifikasi awal sebelumnya.',
                icon: 'info',
                confirmButtonText: 'OK'
            });
        <?php endif; ?>

        $('#submitFormulir').on('click', function(e) {
            e.preventDefault(); // Prevent the default form submission

            // Get the values of the date fields
            var tglLayanan = $('#tgl-layanan-farmasi').val();
            var tglJatuhTempo = $('#tgl-jatuh-tempo').val();

            // Check if either date field is empty
            if (!tglLayanan || !tglJatuhTempo) {
                // Show SweetAlert notification
                Swal.fire({
                    title: 'Peringatan!',
                    text: 'Harap isi semua kolom tanggal.',
                    icon: 'warning',
                    confirmButtonText: 'OK'
                });
                return; // Stop the function if validation fails
            }

            // Check if any checkbox is selected
            // if ($('input[name="input_resep[]"]:checked').length === 0) {
            //     // Show SweetAlert notification for checkboxes
            //     Swal.fire({
            //         title: 'Peringatan!',
            //         text: 'Harap pilih setidaknya satu resep.',
            //         icon: 'warning',
            //         confirmButtonText: 'OK'
            //     });
            //     return; // Stop the function if validation fails
            // }

            // Collect form data
            var formData = new FormData($('#formulirFarmasi')[0]);
            // Add CSRF token to the form data
            formData.append('<?= $csrf_token ?>', '<?= $csrf_hash ?>');

            // Add selected/unselected arrays to form data
            formData.append('selectedResep', JSON.stringify(selectedResep || []));
            formData.append('unselectedResep', JSON.stringify(unselectedResep || []));
            formData.append('selectedPK', JSON.stringify(selectedPK || []));
            formData.append('unselectedPK', JSON.stringify(unselectedPK || []));
            formData.append('selectedDataLama', JSON.stringify(selectedDataLama || []));
            formData.append('unselectedDataLama', JSON.stringify(unselectedDataLama || []));
            formData.append('selectedSito', JSON.stringify(selectedSito || []));
            formData.append('unselectedSito', JSON.stringify(unselectedSito || []));
            formData.append('selectedHisto', JSON.stringify(selectedHisto || []));
            formData.append('unselectedHisto', JSON.stringify(unselectedHisto || []));
            formData.append('selectedImuno', JSON.stringify(selectedImuno || []));
            formData.append('unselectedImuno', JSON.stringify(unselectedImuno || []));
            formData.append('selectedMole', JSON.stringify(selectedMole || []));
            formData.append('unselectedMole', JSON.stringify(unselectedMole || []));
            formData.append('selectedRadiologi1', JSON.stringify(selectedRadiologi1 || []));
            formData.append('unselectedRadiologi1', JSON.stringify(unselectedRadiologi1 || []));
            formData.append('selectedRadiologi2', JSON.stringify(selectedRadiologi2 || []));
            formData.append('unselectedRadiologi2', JSON.stringify(unselectedRadiologi2 || []));

            // Show loading animation
            Swal.fire({
                title: 'Sedang memproses...',
                html: 'Silakan tunggu sebentar.',
                showConfirmButton: false // Hide confirm button
            });

            // Send form data using AJAX
            $.ajax({
                url: '<?= base_url('Farmasi/submitFormulir') ?>',
                type: 'POST',
                data: formData,
                contentType: false,
                processData: false,
                success: function(response) {
                    // Parse the JSON response
                    var result = JSON.parse(response);

                    // Close loading animation
                    Swal.close();

                    // Check if submission is successful
                    if (result.status === 'success') {
                        Swal.fire({
                            title: 'Sukses!',
                            text: result.message,
                            icon: 'success',
                            confirmButtonText: 'OK'
                        }).then((result) => {
                            window.location.href = '<?= base_url('Farmasi/data') ?>';
                        });
                    } else {
                        // Show error alert
                        Swal.fire({
                            title: 'Error!',
                            text: result.message,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function(xhr, status, error) {
                    // Close loading animation
                    Swal.close();

                    // Show error alert if something goes wrong
                    Swal.fire({
                        title: 'Error!',
                        text: 'Terjadi kesalahan saat mengirimkan data.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        });

        //datatable hasil sito
        let sitoInitializedFromServer = false;
        let tableSito = $('#tableSito').DataTable();
        tableSito.clear().destroy();

        // Initialize selectedSito with checked IDs from the first AJAX response
        $('#tableSito').on('xhr.dt', function(e, settings, json, xhr) {
            if (json && json.data && !sitoInitializedFromServer) {
                // Only initialize from server data on first load
                json.data.forEach(function(row) {
                    // Check if the row contains a checked checkbox (assuming it's in the last column)
                    if (row[4] && row[4].includes('checked')) {
                        const match = row[4].match(/value=['\"](\d+)['\"]/);
                        if (match && match[1]) {
                            selectedSito.push(match[1]);
                        }
                    }
                });
                sitoInitializedFromServer = true;
            }
        });

        // Optimized Sitologi table configuration
        const sitoConfig = {
            responsive: true,
            pageLength: 10,
            processing: true,
            serverSide: true,
            lengthChange: false, // Disable length change for simplicity
            ordering: false,
            searching: false, // Disable search for better performance
            deferRender: true,
            language: {
                "processing": 'Memuat Data...',
                "zeroRecords": "Data Tidak Ditemukan",
                "emptyTable": "Data Tidak Tersedia",
                "loadingRecords": "Harap Tunggu...",
                "paginate": {
                    "next": "Selanjutnya",
                    "previous": "Sebelumnya"
                },
                "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ Data",
                "infoEmpty": "Menampilkan 0 sampai 0 dari 0 Data"
            },
            ajax: {
                url: '<?php echo base_url('farmasi/get_data_sito') ?>',
                type: 'POST',
                data: {
                    mr: mr,
                    [csrf_name]: csrf_hash
                },
                error: function(xhr, error, thrown) {
                    console.error('Sitologi Table Ajax Error:', error);
                }
            },
            drawCallback: function() {
                // Optimized checkbox state restoration
                const checkboxes = this.api().table().container().querySelectorAll('.cekSito');
                checkboxes.forEach(function(checkbox) {
                    const id = checkbox.value;
                    if (unselectedSito.includes(id)) {
                        checkbox.checked = false;
                    } else if (selectedSito.includes(id)) {
                        checkbox.checked = true;
                    }
                });
            }
        };

        tableSito = $('#tableSito').DataTable(sitoConfig);

        $(document).on('change', '.cekSito', function() {
            const id = $(this).val();
            if ($(this).is(':checked')) {
                if (!selectedSito.includes(id)) {
                    selectedSito.push(id);
                }
                // Remove from unselected if it was there
                unselectedSito = unselectedSito.filter(item => item !== id);
            } else {
                if (!unselectedSito.includes(id)) {
                    unselectedSito.push(id);
                }
                // Remove from selected if it was there
                selectedSito = selectedSito.filter(item => item !== id);
            }
        });



        //datatable hasil histo
        let histoInitializedFromServer = false;
        let tableHisto = $('#tableHisto').DataTable();
        tableHisto.clear().destroy();

        // Initialize selectedHisto with checked IDs from the first AJAX response
        $('#tableHisto').on('xhr.dt', function(e, settings, json, xhr) {
            if (json && json.data && !histoInitializedFromServer) {
                // Only initialize from server data on first load
                json.data.forEach(function(row) {
                    // Check if the row contains a checked checkbox (assuming it's in the last column)
                    if (row[4] && row[4].includes('checked')) {
                        const match = row[4].match(/value=['\"](\d+)['\"]/);
                        if (match && match[1]) {
                            selectedHisto.push(match[1]);
                        }
                    }
                });
                histoInitializedFromServer = true;
            }
        });

        tableHisto = $('#tableHisto').DataTable({
            responsive: true,
            pageLength: 10,
            processing: true,
            serverSide: true,
            bLengthChange: false,
            ordering: false,
            filter: false,
            order: [],
            language: {
                "processing": 'Memuat Data...',
                "zeroRecords": "Data Tidak Ditemukan",
                "emptyTable": "Data Tidak Tersedia",
                "loadingRecords": "Harap Tunggu...",
                "paginate": {
                    "next": "Selanjutnya",
                    "previous": "Sebelumnya"
                },
                "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ Data",
                "infoEmpty": "Menampilkan 0 sampai 0 dari 0 Data",
                "search": "Cari:",
                "lengthMenu": "Tampilkan: _MENU_ Data",
                "infoFiltered": "(Disaring dari _MAX_ jumlah Data)",
            },
            lengthMenu: [
                [10, 20, 30, 40, 50],
                [10, 20, 30, 40, 50]
            ],
            ajax: {
                url: '<?php echo base_url('farmasi/get_data_histo') ?>',
                type: 'POST',
                data: {
                    mr: mr,
                    [csrf_name]: csrf_hash
                }
            },
            drawCallback: function() {
                $('.cekHisto').each(function() {
                    const id = $(this).val();
                    // First check if user explicitly unchecked this item
                    if (unselectedHisto.includes(id)) {
                        $(this).prop('checked', false);
                    }
                    // Then check if user selected this item or it was pre-selected
                    else if (selectedHisto.includes(id)) {
                        $(this).prop('checked', true);
                    }
                    // For items not in either array, keep the server-provided state
                });
            }
        });

        $(document).on('change', '.cekHisto', function() {
            const id = $(this).val();
            if ($(this).is(':checked')) {
                if (!selectedHisto.includes(id)) {
                    selectedHisto.push(id);
                }
                // Remove from unselected if it was there
                unselectedHisto = unselectedHisto.filter(item => item !== id);
            } else {
                if (!unselectedHisto.includes(id)) {
                    unselectedHisto.push(id);
                }
                // Remove from selected if it was there
                selectedHisto = selectedHisto.filter(item => item !== id);
            }
        });

        //datatable hasil imuno
        let imunoInitializedFromServer = false;
        let tableImuno = $('#tableImuno').DataTable();
        tableImuno.clear().destroy();

        // Initialize selectedImuno with checked IDs from the first AJAX response
        $('#tableImuno').on('xhr.dt', function(e, settings, json, xhr) {
            if (json && json.data && !imunoInitializedFromServer) {
                // Only initialize from server data on first load
                json.data.forEach(function(row) {
                    // Check if the row contains a checked checkbox (assuming it's in the last column)
                    if (row[4] && row[4].includes('checked')) {
                        const match = row[4].match(/value=['\"](\d+)['\"]/);
                        if (match && match[1]) {
                            selectedImuno.push(match[1]);
                        }
                    }
                });
                imunoInitializedFromServer = true;
            }
        });

        tableImuno = $('#tableImuno').DataTable({
            responsive: true,
            pageLength: 10,
            processing: true,
            serverSide: true,
            bLengthChange: false,
            ordering: false,
            filter: false,
            order: [],
            language: {
                "processing": 'Memuat Data...',
                "zeroRecords": "Data Tidak Ditemukan",
                "emptyTable": "Data Tidak Tersedia",
                "loadingRecords": "Harap Tunggu...",
                "paginate": {
                    "next": "Selanjutnya",
                    "previous": "Sebelumnya"
                },
                "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ Data",
                "infoEmpty": "Menampilkan 0 sampai 0 dari 0 Data",
                "search": "Cari:",
                "lengthMenu": "Tampilkan: _MENU_ Data",
                "infoFiltered": "(Disaring dari _MAX_ jumlah Data)",
            },
            lengthMenu: [
                [10, 20, 30, 40, 50],
                [10, 20, 30, 40, 50]
            ],
            ajax: {
                url: '<?php echo base_url('farmasi/get_data_imuno') ?>',
                type: 'POST',
                data: {
                    mr: mr,
                    [csrf_name]: csrf_hash
                }
            },
            drawCallback: function() {
                // Restore checked checkboxes
                $('.cekImuno').each(function() {
                    const id = $(this).val();
                    // First check if user explicitly unchecked this item
                    if (unselectedImuno.includes(id)) {
                        $(this).prop('checked', false);
                    }
                    // Then check if user selected this item or it was pre-selected
                    else if (selectedImuno.includes(id)) {
                        $(this).prop('checked', true);
                    }
                    // For items not in either array, keep the server-provided state
                });
            }
        });

        $(document).on('change', '.cekImuno', function() {
            const id = $(this).val();
            if ($(this).is(':checked')) {
                if (!selectedImuno.includes(id)) {
                    selectedImuno.push(id);
                }
                // Remove from unselected if it was there
                unselectedImuno = unselectedImuno.filter(item => item !== id);
            } else {
                if (!unselectedImuno.includes(id)) {
                    unselectedImuno.push(id);
                }
                // Remove from selected if it was there
                selectedImuno = selectedImuno.filter(item => item !== id);
            }
        });

        //datatable Molekuler
        let moleInitializedFromServer = false;

        let tableMole = $('#tableMole').DataTable();
        tableMole.clear().destroy();

        // Initialize selectedMole with checked IDs from the first AJAX response
        $('#tableMole').on('xhr.dt', function(e, settings, json, xhr) {
            if (json && json.data && !moleInitializedFromServer) {
                // Only initialize from server data on first load
                json.data.forEach(function(row) {
                    // Check if the row contains a checked checkbox (assuming it's in the last column)
                    if (row[4] && row[4].includes('checked')) {
                        const match = row[4].match(/value=['\"](\d+)['\"]/);
                        if (match && match[1]) {
                            selectedMole.push(match[1]);
                        }
                    }
                });
                moleInitializedFromServer = true;
            }
        });

        tableMole = $('#tableMole').DataTable({
            responsive: true,
            pageLength: 10,
            processing: true,
            serverSide: true,
            bLengthChange: false,
            ordering: false,
            filter: false,
            order: [],
            language: {
                "processing": 'Memuat Data...',
                "zeroRecords": "Data Tidak Ditemukan",
                "emptyTable": "Data Tidak Tersedia",
                "loadingRecords": "Harap Tunggu...",
                "paginate": {
                    "next": "Selanjutnya",
                    "previous": "Sebelumnya"
                },
                "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ Data",
                "infoEmpty": "Menampilkan 0 sampai 0 dari 0 Data",
                "search": "Cari:",
                "lengthMenu": "Tampilkan: _MENU_ Data",
                "infoFiltered": "(Disaring dari _MAX_ jumlah Data)",
            },
            lengthMenu: [
                [10, 20, 30, 40, 50],
                [10, 20, 30, 40, 50]
            ],
            ajax: {
                url: '<?php echo base_url('farmasi/get_data_mole') ?>',
                type: 'POST',
                data: {
                    mr: mr,
                    [csrf_name]: csrf_hash
                },
            },
            drawCallback: function() {
                $('.cekMole').each(function() {
                    const id = $(this).val();
                    // First check if user explicitly unchecked this item
                    if (unselectedMole.includes(id)) {
                        $(this).prop('checked', false);
                    }
                    // Then check if user selected this item or it was pre-selected
                    else if (selectedMole.includes(id)) {
                        $(this).prop('checked', true);
                    }
                    // For items not in either array, keep the server-provided state
                });
            }
        });

        $(document).on('change', '.cekMole', function() {
            const id = $(this).val();
            if ($(this).is(':checked')) {
                if (!selectedMole.includes(id)) {
                    selectedMole.push(id);
                }
                // Remove from unselected if it was there
                unselectedMole = unselectedMole.filter(item => item !== id);
            } else {
                if (!unselectedMole.includes(id)) {
                    unselectedMole.push(id);
                }
                // Remove from selected if it was there
                selectedMole = selectedMole.filter(item => item !== id);
            }
        });

        $('#tableSito').on('click', '.cetakSito', function() {
            var id = $(this).attr('data');

            window.requestPrint({
                NAME: "layanan.CetakHasilPaSitologi",
                TYPE: 'Pdf', //Word
                EXT: 'pdf', //docs
                PARAMETER: {
                    PID: id,
                },
                REQUEST_FOR_PRINT: false, //true = print lansung false = view
                PRINT_NAME: "CetakPatologi",
                CONNECTION_NUMBER: 0,
                COPIES: 1,
            });
        });
        $('#tableHisto').on('click', '.cetakHisto', function() {
            var id = $(this).attr('data');

            window.requestPrint({
                NAME: "layanan.CetakHasilPa",
                TYPE: 'Pdf', //Word
                EXT: 'pdf', //docs
                PARAMETER: {
                    PID: id,
                },
                REQUEST_FOR_PRINT: false, //true = print lansung false = view
                PRINT_NAME: "CetakPatologi",
                CONNECTION_NUMBER: 0,
                COPIES: 1,
            });
        });

        $('#tableImuno').on('click', '.cetakImuno', function() {
            var id = $(this).attr('data');

            window.requestPrint({
                NAME: "layanan.CetakHasilPaIHK",
                TYPE: 'Pdf', //Word
                EXT: 'pdf', //docs
                PARAMETER: {
                    PID: id,
                },
                REQUEST_FOR_PRINT: false, //true = print lansung false = view
                PRINT_NAME: "CetakPatologi",
                CONNECTION_NUMBER: 0,
                COPIES: 1,
            });
        });



        $('#tableMole').on('click', '.cetakMole', function() {
            var id = $(this).attr('data');
            var jenis = $(this).attr('jenis');
            var desk = $(this).attr('desk');

            if (jenis == 1 || jenis == 4 || jenis == 5 || jenis == 6 || jenis == 7) {
                var link = 'layanan.Egfr';
            } else if (jenis == 2 || jenis == 8) {
                var link = 'layanan.HpvGenotyping';
            } else if (jenis == 3) {
                var link = 'layanan.HpvDna';
            }

            window.requestPrint({
                NAME: link,
                TYPE: 'Pdf', //Word
                EXT: 'pdf', //docs
                PARAMETER: {
                    IDPATMOL: id,
                    JENIS: jenis
                },
                REQUEST_FOR_PRINT: false, //true = print lansung false = view
                PRINT_NAME: "CetakPatologi",
                CONNECTION_NUMBER: 0,
                COPIES: 1,
            });

        });

        let radiologi1InitializedFromServer = false;
        let radiologi2InitializedFromServer = false;

        // Initialize selectedRadiologi1 with checked IDs from the first AJAX response
        $('#tableRadiologi').on('xhr.dt', function(e, settings, json, xhr) {
            if (json && json.data && !radiologi1InitializedFromServer) {
                // Only initialize from server data on first load
                json.data.forEach(function(row) {
                    // Check if the row contains a checked checkbox (assuming it's in the first column)
                    if (row[0] && row[0].includes('checked')) {
                        const match = row[0].match(/value=['\"](\d+)['\"]/);
                        if (match && match[1]) {
                            selectedRadiologi1.push(match[1]);
                        }
                    }
                });
                radiologi1InitializedFromServer = true;
            }
        });

        $('#tableRadiologi').DataTable({
            responsive: true,
            order: [1, 'desc'],
            language: {
                "processing": 'Memuat Data...',
                "zeroRecords": "Data Tidak Ditemukan",
                "emptyTable": "Data Tidak Tersedia",
                "loadingRecords": "Harap Tunggu...",
                "paginate": {
                    "next": "Selanjutnya",
                    "previous": "Sebelumnya"
                },
                "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ Data",
                "infoEmpty": "Menampilkan 0 sampai 0 dari 0 Data",
                "search": "Cari:",
                "lengthMenu": "Tampilkan: _MENU_ Data",
                "infoFiltered": "(Disaring dari _MAX_ jumlah Data)",
            },

            clear: true,
            destroy: true,
            processing: true,
            serverSide: true,
            iDisplayLength: 10,
            search: {
                regex: true
            },

            // Mulai ambil data
            ajax: {
                url: "<?= base_url('Farmasi/tableRadiologi') ?>",
                type: 'POST',
                data: {
                    [csrf_name]: csrf_hash,
                    norm: mr,
                    part: 1
                }
            },
            drawCallback: function() {
                $('.checkRadiologi').each(function() {
                    const id = $(this).val();
                    const part = $(this).attr('name').includes('input_radiologi2') ? 2 : 1;

                    if (part === 1) {
                        // First check if user explicitly unchecked this item
                        if (unselectedRadiologi1.includes(id)) {
                            $(this).prop('checked', false);
                        }
                        // Then check if user selected this item or it was pre-selected
                        else if (selectedRadiologi1.includes(id)) {
                            $(this).prop('checked', true);
                        }
                    }
                });
            },
            // Akhir ambil data

            // Mulai pendefinisian kolom
            columnDefs: [{
                targets: [0, 3],
                orderable: false,
            }],
            // Akhir pendefinisian kolom
        });

        // Initialize selectedRadiologi2 with checked IDs from the first AJAX response
        $('#tableRadiologi2').on('xhr.dt', function(e, settings, json, xhr) {
            if (json && json.data && !radiologi2InitializedFromServer) {
                // Only initialize from server data on first load
                json.data.forEach(function(row) {
                    // Check if the row contains a checked checkbox (assuming it's in the first column)
                    if (row[0] && row[0].includes('checked')) {
                        const match = row[0].match(/value=['\"](\d+)['\"]/);
                        if (match && match[1]) {
                            selectedRadiologi2.push(match[1]);
                        }
                    }
                });
                radiologi2InitializedFromServer = true;
            }
        });

        $('#tableRadiologi2').DataTable({
            responsive: true,
            order: [1, 'desc'],
            language: {
                "processing": 'Memuat Data...',
                "zeroRecords": "Data Tidak Ditemukan",
                "emptyTable": "Data Tidak Tersedia",
                "loadingRecords": "Harap Tunggu...",
                "paginate": {
                    "next": "Selanjutnya",
                    "previous": "Sebelumnya"
                },
                "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ Data",
                "infoEmpty": "Menampilkan 0 sampai 0 dari 0 Data",
                "search": "Cari:",
                "lengthMenu": "Tampilkan: _MENU_ Data",
                "infoFiltered": "(Disaring dari _MAX_ jumlah Data)",
            },

            clear: true,
            destroy: true,
            processing: true,
            serverSide: true,
            iDisplayLength: 10,
            search: {
                regex: true
            },

            // Mulai ambil data
            ajax: {
                url: "<?= base_url('Farmasi/tableRadiologi') ?>",
                type: 'POST',
                data: {
                    [csrf_name]: csrf_hash,
                    norm: mr,
                    part: 2
                }
            },
            drawCallback: function() {
                $('.checkRadiologi').each(function() {
                    const id = $(this).val();
                    const part = $(this).attr('name').includes('input_radiologi2') ? 2 : 1;

                    if (part === 2) {
                        // First check if user explicitly unchecked this item
                        if (unselectedRadiologi2.includes(id)) {
                            $(this).prop('checked', false);
                        }
                        // Then check if user selected this item or it was pre-selected
                        else if (selectedRadiologi2.includes(id)) {
                            $(this).prop('checked', true);
                        }
                    }
                });
            },
            // Akhir ambil data

            // Mulai pendefinisian kolom
            columnDefs: [{
                targets: [0, 3],
                orderable: false,
            }],
            // Akhir pendefinisian kolom
        });

        $(document).on('change', '.checkRadiologi', function() {
            const id = $(this).val();
            const part = $(this).attr('name').includes('input_radiologi2') ? 2 : 1;

            if ($(this).is(':checked')) {
                if (part === 1) {
                    if (!selectedRadiologi1.includes(id)) selectedRadiologi1.push(id);
                    unselectedRadiologi1 = unselectedRadiologi1.filter(item => item !== id);
                }
                if (part === 2) {
                    if (!selectedRadiologi2.includes(id)) selectedRadiologi2.push(id);
                    unselectedRadiologi2 = unselectedRadiologi2.filter(item => item !== id);
                }
            } else {
                if (part === 1) {
                    if (!unselectedRadiologi1.includes(id)) unselectedRadiologi1.push(id);
                    selectedRadiologi1 = selectedRadiologi1.filter(item => item !== id);
                }
                if (part === 2) {
                    if (!unselectedRadiologi2.includes(id)) unselectedRadiologi2.push(id);
                    selectedRadiologi2 = selectedRadiologi2.filter(item => item !== id);
                }
            }
        });


        $('.tableRadiologi').on('click', '.btn-cetak', function() {
            let id = $(this).data('id');

            window.requestPrint({
                NAME: 'layanan.CetakHasilRadCop',
                TYPE: 'Pdf', // Word
                EXT: 'pdf', // docs
                PARAMETER: {
                    PTINDAKAN: `${id}`, // backtick buat jadiin string
                },
                REQUEST_FOR_PRINT: false, // true = print lansung false = view
                PRINT_NAME: 'CetakRadiologi',
                CONNECTION_NUMBER: 0,
                COPIES: 1,
            });
        });
        $('.tableRadiologi').on('click', '.tbl-expertise-farmasi', function() {
            $.ajax({
                type: 'POST',
                url: "<?= base_url('Radiologi/lihatExpertise') ?>",
                data: {
                    [csrf_name]: csrf_hash,
                    id: $(this).data('tindakan')
                },
                success: function(data) {
                    $('#lihat-expertise-farmasi').html(data);
                }
            });
        });

        $(document).on('click', '.viewPk', function() {
            var nokun = $(this).data('id');
            $.ajax({
                type: 'POST',
                url: "<?php echo base_url('farmasi/view_pk') ?>",
                data: {
                    [csrf_name]: csrf_hash,
                    nokun: nokun
                },
                success: function(data) {
                    $('#myModalPk').html(data);
                    $('#myModalPk').modal('show');
                }
            });
        });
    });
    $(document).on('click', '.delete-btn', function() {
        const fileId = $(this).data('id'); // Get the file ID from data-id attribute
        let csrf_name = '<?= $csrf_token ?>';
        let csrf_hash = '<?= $csrf_hash ?>';
        Swal.fire({
            title: 'Konfirmasi Hapus',
            text: "Apakah Anda yakin ingin menghapus file ini?",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Ya, hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.value) {
                // Make AJAX call to delete the file
                $.ajax({
                    url: '<?= base_url("farmasi/deleteFile") ?>',
                    type: 'POST',
                    data: {
                        [csrf_name]: csrf_hash,
                        id_file: fileId,
                    },
                    success: function(response) {
                        const res = JSON.parse(response);
                        if (res.status === 'success') {
                            Swal.fire(
                                'Terhapus!',
                                res.message,
                                'success'
                            ).then(() => {
                                // Optionally, reload the DataTable or remove the deleted row
                                location.reload(); // Reload the page or update DataTable as needed
                            });
                        } else {
                            Swal.fire(
                                'Gagal!',
                                res.message,
                                'error'
                            );
                        }
                    },
                    error: function() {
                        Swal.fire(
                            'Gagal!',
                            'Terjadi kesalahan saat menghapus file.',
                            'error'
                        );
                    }
                });
            }
        });
    });
</script>
<script src="<?= base_url('assets/plugins/bs-stepper/js/bs-stepper.min.js') ?>"></script>